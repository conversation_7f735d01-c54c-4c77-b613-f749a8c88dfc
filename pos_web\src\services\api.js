import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000/api', // Adjust this to your Laravel backend URL
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/login', credentials),
  register: (userData) => api.post('/register', userData),
  logout: () => api.post('/logout'),
  me: () => api.get('/user'),
  refreshPermissions: () => api.post('/refresh-permissions'),
};

// Products API
export const productsAPI = {
  getAll: (params = {}) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  create: (data) => api.post('/products', data),
  update: (id, data) => api.put(`/products/${id}`, data),
  delete: (id) => api.delete(`/products/${id}`),
  getLowStock: () => api.get('/products/low-stock'),
  getWithPrices: (id) => api.get(`/products/${id}/with-prices`),
};

// Categories API
export const categoriesAPI = {
  getAll: () => api.get('/categories'),
  getById: (id) => api.get(`/categories/${id}`),
  create: (data) => api.post('/categories', data),
  update: (id, data) => api.put(`/categories/${id}`, data),
  delete: (id) => api.delete(`/categories/${id}`),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  create: (data) => api.post('/orders', data),
  update: (id, data) => api.put(`/orders/${id}`, data),
  delete: (id) => api.delete(`/orders/${id}`),
  getStatistics: () => api.get('/orders/statistics'),
};

// Customer Orders API
export const customerOrdersAPI = {
  getAll: (params = {}) => api.get('/customer-orders', { params }),
  getById: (id) => api.get(`/customer-orders/${id}`),
  create: (data) => api.post('/customer-orders', data),
  cancel: (id) => api.post(`/customer-orders/${id}/cancel`),
};

// Customers API
export const customersAPI = {
  getAll: (params = {}) => api.get('/customers', { params }),
  getById: (id) => api.get(`/customers/${id}`),
  create: (data) => api.post('/customers', data),
  update: (id, data) => api.put(`/customers/${id}`, data),
  delete: (id) => api.delete(`/customers/${id}`),
};

// Users API
export const usersAPI = {
  getAll: (params = {}) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  create: (data) => api.post('/users', data),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`),
};

// Payments API
export const paymentsAPI = {
  getAll: (params = {}) => api.get('/payments', { params }),
  getById: (id) => api.get(`/payments/${id}`),
  create: (data) => api.post('/payments', data),
  update: (id, data) => api.put(`/payments/${id}`, data),
  delete: (id) => api.delete(`/payments/${id}`),
  process: (id) => api.post(`/payments/${id}/process`),
};

// Deliveries API
export const deliveriesAPI = {
  getAll: (params = {}) => api.get('/deliveries', { params }),
  getById: (id) => api.get(`/deliveries/${id}`),
  create: (data) => api.post('/deliveries', data),
  update: (id, data) => api.put(`/deliveries/${id}`, data),
  delete: (id) => api.delete(`/deliveries/${id}`),
  assignDriver: (id, driverData) => api.post(`/deliveries/${id}/assign-driver`, driverData),
  updateStatus: (id, statusData) => api.post(`/deliveries/${id}/update-status`, statusData),
  updateLocation: (id, locationData) => api.post(`/deliveries/${id}/update-location`, locationData),
};

// Reports API
export const reportsAPI = {
  sales: (params = {}) => api.get('/reports/sales', { params }),
  inventory: (params = {}) => api.get('/reports/inventory', { params }),
  buyInSellOut: (params = {}) => api.get('/reports/buy-in-sell-out', { params }),
  customerFeedback: (params = {}) => api.get('/reports/customer-feedback', { params }),
  deliveryPerformance: (params = {}) => api.get('/reports/delivery-performance', { params }),
};

// Notifications API
export const notificationsAPI = {
  getAll: () => api.get('/notifications'),
  stockAlert: (data) => api.post('/notifications/stock-alert', data),
  orderStatusUpdate: (data) => api.post('/notifications/order-status', data),
  customerFeedback: (data) => api.post('/notifications/customer-feedback', data),
  deliveryUpdate: (data) => api.post('/notifications/delivery-update', data),
};

// Permissions API
export const permissionsAPI = {
  getAll: () => api.get('/permissions'),
  getById: (id) => api.get(`/permissions/${id}`),
  create: (data) => api.post('/permissions', data),
  update: (id, data) => api.put(`/permissions/${id}`, data),
  delete: (id) => api.delete(`/permissions/${id}`),
  bulkCreate: (data) => api.post('/permissions/bulk-create', data),
  
  // Roles
  getRoles: () => api.get('/permissions/roles/list'),
  createRole: (data) => api.post('/permissions/roles', data),
  getRole: (id) => api.get(`/permissions/roles/${id}`),
  updateRole: (id, data) => api.put(`/permissions/roles/${id}`, data),
  deleteRole: (id) => api.delete(`/permissions/roles/${id}`),
  
  // Role-Permission Management
  assignPermissionsToRole: (roleId, permissions) => 
    api.post(`/permissions/roles/${roleId}/assign-permissions`, { permissions }),
  removePermissionsFromRole: (roleId, permissions) => 
    api.post(`/permissions/roles/${roleId}/remove-permissions`, { permissions }),
  
  // User-Role Management
  assignRolesToUser: (userId, roles) => 
    api.post(`/permissions/users/${userId}/assign-roles`, { roles }),
  removeRolesFromUser: (userId, roles) => 
    api.post(`/permissions/users/${userId}/remove-roles`, { roles }),
  
  // User-Permission Management
  assignPermissionsToUser: (userId, permissions) => 
    api.post(`/permissions/users/${userId}/assign-permissions`, { permissions }),
  removePermissionsFromUser: (userId, permissions) => 
    api.post(`/permissions/users/${userId}/remove-permissions`, { permissions }),
  
  // User Permission Queries
  getUserPermissions: (userId) => api.get(`/permissions/users/${userId}/permissions`),
  checkUserPermission: (userId, permission) => 
    api.post(`/permissions/users/${userId}/check-permission`, { permission }),
  checkUserRole: (userId, role) => 
    api.post(`/permissions/users/${userId}/check-role`, { role }),
  getUsersWithPermissions: () => api.get('/permissions/users/with-permissions'),
};

export default api;
