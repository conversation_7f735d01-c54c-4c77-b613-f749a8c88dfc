{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\pages\\\\Products.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Products", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/pages/Products.js"], "sourcesContent": ["import React from 'react'\n\nconst Products = () => {\n  return (\n    <div>\n      \n    </div>\n  )\n}\n\nexport default Products\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEK,CAAC;AAEV,CAAC;AAAAC,EAAA,GANKL,QAAQ;AAQd,eAAeA,QAAQ;AAAA,IAAAK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}