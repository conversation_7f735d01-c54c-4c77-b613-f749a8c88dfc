import React from "react";
import { <PERSON><PERSON>ircle, XCircle, <PERSON><PERSON><PERSON><PERSON>gle, Info, X } from "lucide-react";

// Mapping styles and icons by status
const statusStyles = {
  success: {
    bg: "bg-green-50",
    text: "text-green-800",
    iconColor: "text-green-600",
    Icon: CheckCircle,
  },
  error: {
    bg: "bg-red-50",
    text: "text-red-800",
    iconColor: "text-red-600",
    Icon: XCircle,
  },
  warning: {
    bg: "bg-yellow-50",
    text: "text-yellow-800",
    iconColor: "text-yellow-600",
    Icon: AlertTriangle,
  },
  info: {
    bg: "bg-blue-50",
    text: "text-blue-800",
    iconColor: "text-blue-600",
    Icon: Info,
  },
};

export const Alert = ({ message, onClose, visible, status = "success" }) => {
  if (!visible) return null;

  const { bg, text, iconColor, Icon } =
    statusStyles[status] || statusStyles.success;

  return (
    <div
      className={`fixed bottom-0 left-3 items-center justify-between p-4 mb-4 rounded-lg shadow-md ${bg} ${text}`}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex items-start bottom-0">
          <Icon className={`w-5 h-5 mr-2 ${iconColor}`} />
          <span className="text-sm font-medium">{message}</span>
        </div>
        <button
          onClick={onClose}
          className={`${iconColor} hover:opacity-80 transition ml-2`}
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};
