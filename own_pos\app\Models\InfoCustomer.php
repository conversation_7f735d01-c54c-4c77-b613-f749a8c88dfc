<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InfoCustomer extends Model
{
    protected $table = 'info_customers';
    protected $primaryKey = 'customer_id';

    protected $fillable = [
        'user_id',
        'first_name_kh',
        'last_name_kh',
        'fullname_kh',
        'gender',
        'date_of_birth',
        'phone',
        'current_address',
        'photo'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the customer info.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the orders for the customer.
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'customer_id', 'customer_id');
    }
}
