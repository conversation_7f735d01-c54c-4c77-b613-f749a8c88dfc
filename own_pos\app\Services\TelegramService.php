<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramService
{
    private $botToken;
    private $chatId;
    private $baseUrl;

    public function __construct()
    {
        $this->botToken = config('services.telegram.bot_token');
        $this->chatId = config('services.telegram.chat_id');
        $this->baseUrl = "https://api.telegram.org/bot{$this->botToken}";
    }

    /**
     * Send a message to Telegram
     */
    public function sendMessage(string $message, array $options = []): bool
    {
        try {
            $payload = [
                'chat_id' => $options['chat_id'] ?? $this->chatId,
                'text' => $message,
                'parse_mode' => $options['parse_mode'] ?? 'HTML',
                'disable_web_page_preview' => $options['disable_preview'] ?? true,
            ];

            $response = Http::post("{$this->baseUrl}/sendMessage", $payload);

            if ($response->successful()) {
                Log::info('Telegram message sent successfully', ['message' => $message]);
                return true;
            } else {
                Log::error('Failed to send Telegram message', [
                    'message' => $message,
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Telegram service error', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send stock alert notification
     */
    public function sendStockAlert(array $lowStockProducts): bool
    {
        if (empty($lowStockProducts)) {
            return true;
        }

        $message = "🚨 <b>LOW STOCK ALERT</b> 🚨\n\n";
        $message .= "The following products are running low on stock:\n\n";

        foreach ($lowStockProducts as $product) {
            $message .= "📦 <b>{$product['name_kh']}</b>";
            if (!empty($product['name_en'])) {
                $message .= " ({$product['name_en']})";
            }
            $message .= "\n";
            $message .= "   Current Stock: <b>{$product['stock_quantity']}</b>\n";
            $message .= "   Minimum Level: <b>{$product['min_stock_level']}</b>\n";
            $message .= "   Category: {$product['category']['name_kh']}\n\n";
        }

        $message .= "⚠️ Please restock these items as soon as possible!";

        return $this->sendMessage($message);
    }

    /**
     * Send order notification
     */
    public function sendOrderNotification(array $orderData, string $type = 'new'): bool
    {
        $icons = [
            'new' => '🆕',
            'completed' => '✅',
            'cancelled' => '❌',
            'preparing' => '👨‍🍳',
            'ready' => '🍽️'
        ];

        $icon = $icons[$type] ?? '📋';
        $typeText = ucfirst($type);

        $message = "{$icon} <b>{$typeText} ORDER</b>\n\n";
        $message .= "Order #: <b>{$orderData['order_number']}</b>\n";
        $message .= "Type: {$orderData['order_type']}\n";
        $message .= "Total: $" . number_format($orderData['total_amount'], 2) . "\n";

        if (!empty($orderData['customer'])) {
            $message .= "Customer: {$orderData['customer']['fullname_kh']}\n";
            if (!empty($orderData['customer']['phone'])) {
                $message .= "Phone: {$orderData['customer']['phone']}\n";
            }
        }

        if ($type === 'new' && !empty($orderData['order_items'])) {
            $message .= "\n<b>Items:</b>\n";
            foreach ($orderData['order_items'] as $item) {
                $message .= "• {$item['quantity']}x {$item['product']['name_kh']}\n";
            }
        }

        $message .= "\nTime: " . date('Y-m-d H:i:s');

        return $this->sendMessage($message);
    }

    /**
     * Send delivery notification
     */
    public function sendDeliveryNotification(array $deliveryData, string $status): bool
    {
        $statusIcons = [
            'assigned' => '🚚',
            'picked_up' => '📦',
            'in_transit' => '🛣️',
            'delivered' => '✅',
            'failed' => '❌'
        ];

        $icon = $statusIcons[$status] ?? '🚚';
        $statusText = ucwords(str_replace('_', ' ', $status));

        $message = "{$icon} <b>DELIVERY {$statusText}</b>\n\n";
        $message .= "Order #: <b>{$deliveryData['order']['order_number']}</b>\n";
        $message .= "Customer: {$deliveryData['order']['customer']['fullname_kh']}\n";
        $message .= "Phone: {$deliveryData['order']['customer']['phone']}\n";

        if (!empty($deliveryData['driver'])) {
            $message .= "Driver: {$deliveryData['driver']['fullname_kh']}\n";
            $message .= "Driver Phone: {$deliveryData['driver']['phone']}\n";
        }

        if (!empty($deliveryData['delivery_address'])) {
            $message .= "Address: {$deliveryData['delivery_address']}\n";
        }

        if ($status === 'delivered' && !empty($deliveryData['delivered_at'])) {
            $message .= "Delivered at: {$deliveryData['delivered_at']}\n";
        }

        $message .= "\nTime: " . date('Y-m-d H:i:s');

        return $this->sendMessage($message);
    }

    /**
     * Send customer feedback notification
     */
    public function sendCustomerFeedback(array $feedbackData): bool
    {
        $ratingStars = str_repeat('⭐', $feedbackData['rating'] ?? 0);
        
        $message = "💬 <b>CUSTOMER FEEDBACK</b>\n\n";
        $message .= "Order #: <b>{$feedbackData['order']['order_number']}</b>\n";
        $message .= "Customer: {$feedbackData['order']['customer']['fullname_kh']}\n";
        $message .= "Rating: {$ratingStars} ({$feedbackData['rating']}/5)\n\n";
        
        if (!empty($feedbackData['comment'])) {
            $message .= "<b>Comment:</b>\n{$feedbackData['comment']}\n\n";
        }
        
        $message .= "Time: " . date('Y-m-d H:i:s');

        return $this->sendMessage($message);
    }

    /**
     * Send daily sales report
     */
    public function sendDailySalesReport(array $reportData): bool
    {
        $message = "📊 <b>DAILY SALES REPORT</b>\n";
        $message .= "Date: " . date('Y-m-d') . "\n\n";
        
        $message .= "💰 Total Revenue: $" . number_format($reportData['total_revenue'], 2) . "\n";
        $message .= "📦 Total Orders: {$reportData['total_orders']}\n";
        $message .= "💵 Average Order: $" . number_format($reportData['avg_order_value'], 2) . "\n\n";
        
        $message .= "<b>Order Status:</b>\n";
        $message .= "✅ Completed: {$reportData['completed_orders']}\n";
        $message .= "⏳ Pending: {$reportData['pending_orders']}\n";
        $message .= "❌ Cancelled: {$reportData['cancelled_orders']}\n\n";
        
        if (!empty($reportData['top_products'])) {
            $message .= "<b>Top Products:</b>\n";
            foreach ($reportData['top_products'] as $index => $product) {
                $message .= ($index + 1) . ". {$product['name']} ({$product['quantity']} sold)\n";
            }
        }

        return $this->sendMessage($message);
    }
}
