{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M2 8h20\",\n  key: \"d11cs7\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"14\",\n  r: \"2\",\n  key: \"1k2qr5\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"14\",\n  r: \"2\",\n  key: \"14k7lr\"\n}]];\nconst Videotape = createLucideIcon(\"videotape\", __iconNode);\nexport { __iconNode, Videotape as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "cx", "cy", "r", "Videotape", "createLucideIcon"], "sources": ["E:\\Developer\\Pos_system\\pos_web\\node_modules\\lucide-react\\src\\icons\\videotape.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'M2 8h20', key: 'd11cs7' }],\n  ['circle', { cx: '8', cy: '14', r: '2', key: '1k2qr5' }],\n  ['path', { d: 'M8 12h8', key: '1wcyev' }],\n  ['circle', { cx: '16', cy: '14', r: '2', key: '14k7lr' }],\n];\n\n/**\n * @component @name Videotape\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yIDhoMjAiIC8+CiAgPGNpcmNsZSBjeD0iOCIgY3k9IjE0IiByPSIyIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDgiIC8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxNCIgcj0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/videotape\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Videotape = createLucideIcon('videotape', __iconNode);\n\nexport default Videotape;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEE,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAU,GAC1D;AAaM,MAAAK,SAAA,GAAYC,gBAAiB,cAAaZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}