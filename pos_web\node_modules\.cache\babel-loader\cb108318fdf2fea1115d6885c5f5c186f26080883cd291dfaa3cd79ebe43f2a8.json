{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\components\\\\auth\\\\ProtectedRoute.js\";\nimport { Navigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  permissions = []\n}) => {\n  try {\n    const userString = sessionStorage.getItem(\"user\");\n    if (!userString) {\n      console.warn(\"User data not found. Redirecting to login.\");\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 14\n      }, this);\n    }\n    const userData = JSON.parse(userString);\n    const {\n      token,\n      user,\n      permissions: userPermissions\n    } = userData;\n    if (!token || !user) {\n      console.warn(\"User data not found. Redirecting to login.\");\n      sessionStorage.removeItem(\"user\");\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 14\n      }, this);\n    }\n    // Handle permissions from the correct location in the response\n    const userPermission = (userPermissions || []).map(permission => {\n      var _permission$name;\n      return typeof permission === \"string\" ? permission.toLowerCase() : (_permission$name = permission.name) === null || _permission$name === void 0 ? void 0 : _permission$name.toLowerCase();\n    }).filter(Boolean);\n    const hasPermission = permissions.every(prem => userPermission.includes(prem.toLowerCase()));\n    if (!hasPermission) {\n      console.warn(\"User does not have required permissions. Redirecting to login.\");\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 14\n      }, this);\n    }\n    return children;\n  } catch (error) {\n    console.error(\"Error checking permissions:\", error);\n    sessionStorage.removeItem(\"user\");\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this);\n  }\n};\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["Navigate", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "permissions", "userString", "sessionStorage", "getItem", "console", "warn", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "userData", "JSON", "parse", "token", "user", "userPermissions", "removeItem", "userPermission", "map", "permission", "_permission$name", "toLowerCase", "name", "filter", "Boolean", "hasPermission", "every", "prem", "includes", "error", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import { Navigate } from \"react-router-dom\";\n\nconst ProtectedRoute = ({ children, permissions = [] }) => {\n  try {\n    const userString = sessionStorage.getItem(\"user\");\n    if (!userString) {\n      console.warn(\"User data not found. Redirecting to login.\");\n      return <Navigate to=\"/login\" replace />;\n    }\n    const userData = JSON.parse(userString);\n    const { token, user, permissions: userPermissions } = userData;\n    if (!token || !user) {\n      console.warn(\"User data not found. Redirecting to login.\");\n      sessionStorage.removeItem(\"user\");\n      return <Navigate to=\"/login\" replace />;\n    }\n    // Handle permissions from the correct location in the response\n    const userPermission = (userPermissions || [])\n      .map((permission) =>\n        typeof permission === \"string\"\n          ? permission.toLowerCase()\n          : permission.name?.toLowerCase()\n      )\n      .filter(Boolean);\n    const hasPermission = permissions.every((prem) =>\n      userPermission.includes(prem.toLowerCase())\n    );\n    if (!hasPermission) {\n      console.warn(\n        \"User does not have required permissions. Redirecting to login.\"\n      );\n      return <Navigate to=\"/login\" replace />;\n    }\n    return children;\n  } catch (error) {\n    console.error(\"Error checking permissions:\", error);\n    sessionStorage.removeItem(\"user\");\n    return <Navigate to=\"/login\" replace />;\n  }\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,WAAW,GAAG;AAAG,CAAC,KAAK;EACzD,IAAI;IACF,MAAMC,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IACjD,IAAI,CAACF,UAAU,EAAE;MACfG,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;MAC1D,oBAAOR,OAAA,CAACF,QAAQ;QAACW,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;IACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACb,UAAU,CAAC;IACvC,MAAM;MAAEc,KAAK;MAAEC,IAAI;MAAEhB,WAAW,EAAEiB;IAAgB,CAAC,GAAGL,QAAQ;IAC9D,IAAI,CAACG,KAAK,IAAI,CAACC,IAAI,EAAE;MACnBZ,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;MAC1DH,cAAc,CAACgB,UAAU,CAAC,MAAM,CAAC;MACjC,oBAAOrB,OAAA,CAACF,QAAQ;QAACW,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;IACA;IACA,MAAMQ,cAAc,GAAG,CAACF,eAAe,IAAI,EAAE,EAC1CG,GAAG,CAAEC,UAAU;MAAA,IAAAC,gBAAA;MAAA,OACd,OAAOD,UAAU,KAAK,QAAQ,GAC1BA,UAAU,CAACE,WAAW,CAAC,CAAC,IAAAD,gBAAA,GACxBD,UAAU,CAACG,IAAI,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBC,WAAW,CAAC,CAAC;IAAA,CACpC,CAAC,CACAE,MAAM,CAACC,OAAO,CAAC;IAClB,MAAMC,aAAa,GAAG3B,WAAW,CAAC4B,KAAK,CAAEC,IAAI,IAC3CV,cAAc,CAACW,QAAQ,CAACD,IAAI,CAACN,WAAW,CAAC,CAAC,CAC5C,CAAC;IACD,IAAI,CAACI,aAAa,EAAE;MAClBvB,OAAO,CAACC,IAAI,CACV,gEACF,CAAC;MACD,oBAAOR,OAAA,CAACF,QAAQ;QAACW,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;IACA,OAAOZ,QAAQ;EACjB,CAAC,CAAC,OAAOgC,KAAK,EAAE;IACd3B,OAAO,CAAC2B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD7B,cAAc,CAACgB,UAAU,CAAC,MAAM,CAAC;IACjC,oBAAOrB,OAAA,CAACF,QAAQ;MAACW,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;AACF,CAAC;AAACqB,EAAA,GArCIlC,cAAc;AAuCpB,eAAeA,cAAc;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}