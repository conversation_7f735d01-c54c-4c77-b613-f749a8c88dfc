{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\components\\\\layout\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { useAuth } from \"../../contexts/AuthContext\";\nimport { authAPI } from \"../../services/api\";\nimport { Menu, Bell, Search, User, LogOut, Settings } from \"lucide-react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onMenuClick\n}) => {\n  _s();\n  var _userData$info, _userData$info2;\n  const {\n    logout\n  } = useAuth();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [userData, setUserData] = useState(null);\n  const navigate = useNavigate();\n  const userMenuRef = useRef(null);\n  const notificationRef = useRef(null);\n  const handleLogout = async () => {\n    await logout();\n    setShowUserMenu(false);\n  };\n\n  // Handle click outside to close menus\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n        setShowUserMenu(false);\n      }\n      if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n        setShowNotifications(false);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const userString = sessionStorage.getItem(\"user\");\n    if (userString) {\n      const storedUserData = JSON.parse(userString);\n      const userId = storedUserData.user.id;\n      const fetchUserData = async () => {\n        try {\n          const response = await authAPI.me(userId);\n          setUserData(response.data);\n          console.log(response);\n        } catch (error) {\n          console.error(\"Error fetching user data:\", error);\n          // Fallback to stored user data if API call fails\n          setUserData(storedUserData.user);\n        }\n      };\n      fetchUserData();\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-sm border-b border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-4 py-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onMenuClick,\n          className: \"p-2 rounded-lg text-gray-600 hover:bg-gray-100 lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block ml-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search...\",\n              className: \"block w-80 pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: notificationRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNotifications(!showNotifications),\n            className: \"p-2 rounded-lg text-gray-600 hover:bg-gray-100 relative\",\n            children: [/*#__PURE__*/_jsxDEV(Bell, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), showNotifications && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-96 overflow-y-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-2 w-2 bg-blue-500 rounded-full mt-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"New order received\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"2 minutes ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-2 w-2 bg-yellow-500 rounded-full mt-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"Low stock alert\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"1 hour ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                  children: \"View all notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: userMenuRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowUserMenu(!showUserMenu),\n            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 w-8 rounded-full flex items-center justify-center\",\n              children: userData !== null && userData !== void 0 && (_userData$info = userData.info) !== null && _userData$info !== void 0 && _userData$info.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:8000/storage/${userData === null || userData === void 0 ? void 0 : (_userData$info2 = userData.info) === null || _userData$info2 === void 0 ? void 0 : _userData$info2.photo}`,\n                alt: \"User\",\n                className: \"h-8 w-8 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(User, {\n                className: \"h-5 w-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: (userData === null || userData === void 0 ? void 0 : userData.name) || \"Admin User\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: (userData === null || userData === void 0 ? void 0 : userData.email) || \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  navigate(\"/admin/profile\");\n                  setShowUserMenu(false);\n                },\n                className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"mr-3 h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this), \"Profile\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  navigate(\"/admin/settings\");\n                  setShowUserMenu(false);\n                },\n                className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(Settings, {\n                  className: \"mr-3 h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), \"Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"my-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\",\n                children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                  className: \"mr-3 h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"kp3Mi87eRTDFzegkEEoRQpMPM1c=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "authAPI", "<PERSON><PERSON>", "Bell", "Search", "User", "LogOut", "Settings", "useNavigate", "jsxDEV", "_jsxDEV", "Header", "onMenuClick", "_s", "_userData$info", "_userData$info2", "logout", "showUserMenu", "setShowUserMenu", "showNotifications", "setShowNotifications", "userData", "setUserData", "navigate", "userMenuRef", "notificationRef", "handleLogout", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "userString", "sessionStorage", "getItem", "storedUserData", "JSON", "parse", "userId", "user", "id", "fetchUserData", "response", "me", "data", "console", "log", "error", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "ref", "info", "photo", "src", "alt", "name", "email", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/components/layout/Header.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { useAuth } from \"../../contexts/AuthContext\";\nimport { authAPI } from \"../../services/api\";\nimport { Menu, Bell, Search, User, LogOut, Settings } from \"lucide-react\";\nimport { useNavigate } from \"react-router-dom\";\nconst Header = ({ onMenuClick }) => {\n  const { logout } = useAuth();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [userData, setUserData] = useState(null);\n  const navigate = useNavigate();\n  const userMenuRef = useRef(null);\n  const notificationRef = useRef(null);\n  const handleLogout = async () => {\n    await logout();\n    setShowUserMenu(false);\n  };\n\n  // Handle click outside to close menus\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n        setShowUserMenu(false);\n      }\n      if (\n        notificationRef.current &&\n        !notificationRef.current.contains(event.target)\n      ) {\n        setShowNotifications(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  useEffect(() => {\n    const userString = sessionStorage.getItem(\"user\");\n    if (userString) {\n      const storedUserData = JSON.parse(userString);\n      const userId = storedUserData.user.id;\n      const fetchUserData = async () => {\n        try {\n          const response = await authAPI.me(userId);\n          setUserData(response.data);\n          console.log(response);\n        } catch (error) {\n          console.error(\"Error fetching user data:\", error);\n          // Fallback to stored user data if API call fails\n          setUserData(storedUserData.user);\n        }\n      };\n      fetchUserData();\n    }\n  }, []);\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"flex items-center justify-between px-4 py-3\">\n        {/* Left side */}\n        <div className=\"flex items-center\">\n          <button\n            onClick={onMenuClick}\n            className=\"p-2 rounded-lg text-gray-600 hover:bg-gray-100 lg:hidden\"\n          >\n            <Menu className=\"h-6 w-6\" />\n          </button>\n\n          {/* Search bar */}\n          <div className=\"hidden md:block ml-4\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block w-80 pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <div className=\"relative\" ref={notificationRef}>\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"p-2 rounded-lg text-gray-600 hover:bg-gray-100 relative\"\n            >\n              <Bell className=\"h-6 w-6\" />\n              <span className=\"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"></span>\n            </button>\n\n            {showNotifications && (\n              <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n                <div className=\"p-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    Notifications\n                  </h3>\n                </div>\n                <div className=\"max-h-96 overflow-y-auto\">\n                  <div className=\"p-4 border-b border-gray-100\">\n                    <div className=\"flex items-start\">\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"h-2 w-2 bg-blue-500 rounded-full mt-2\"></div>\n                      </div>\n                      <div className=\"ml-3\">\n                        <p className=\"text-sm text-gray-900\">\n                          New order received\n                        </p>\n                        <p className=\"text-xs text-gray-500\">2 minutes ago</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 border-b border-gray-100\">\n                    <div className=\"flex items-start\">\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"h-2 w-2 bg-yellow-500 rounded-full mt-2\"></div>\n                      </div>\n                      <div className=\"ml-3\">\n                        <p className=\"text-sm text-gray-900\">Low stock alert</p>\n                        <p className=\"text-xs text-gray-500\">1 hour ago</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4\">\n                    <button className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800\">\n                      View all notifications\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* User menu */}\n          <div className=\"relative\" ref={userMenuRef}>\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100\"\n            >\n              <div className=\"h-8 w-8 rounded-full flex items-center justify-center\">\n                {userData?.info?.photo ? (\n                  <img\n                    src={`http://localhost:8000/storage/${userData?.info?.photo}`}\n                    alt=\"User\"\n                    className=\"h-8 w-8 rounded-full\"\n                  />\n                ) : (\n                  <User className=\"h-5 w-5 text-white\" />\n                )}\n              </div>\n              <div className=\"hidden md:block text-left\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  {userData?.name || \"Admin User\"}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  {userData?.email || \"<EMAIL>\"}\n                </p>\n              </div>\n            </button>\n\n            {showUserMenu && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => {\n                      navigate(\"/admin/profile\");\n                      setShowUserMenu(false);\n                    }}\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <User className=\"mr-3 h-4 w-4\" />\n                    Profile\n                  </button>\n                  <button\n                    onClick={() => {\n                      navigate(\"/admin/settings\");\n                      setShowUserMenu(false);\n                    }}\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Settings className=\"mr-3 h-4 w-4\" />\n                    Settings\n                  </button>\n                  <hr className=\"my-1\" />\n                  <button\n                    onClick={handleLogout}\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                  >\n                    <LogOut className=\"mr-3 h-4 w-4\" />\n                    Logout\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AACzE,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,eAAA;EAClC,MAAM;IAAEC;EAAO,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC5B,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM0B,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,WAAW,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM0B,eAAe,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMV,MAAM,CAAC,CAAC;IACdE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACApB,SAAS,CAAC,MAAM;IACd,MAAM6B,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIJ,WAAW,CAACK,OAAO,IAAI,CAACL,WAAW,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEb,eAAe,CAAC,KAAK,CAAC;MACxB;MACA,IACEO,eAAe,CAACI,OAAO,IACvB,CAACJ,eAAe,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAC/C;QACAX,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAEDY,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN7B,SAAS,CAAC,MAAM;IACd,MAAMqC,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IACjD,IAAIF,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MAC7C,MAAMM,MAAM,GAAGH,cAAc,CAACI,IAAI,CAACC,EAAE;MACrC,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAM5C,OAAO,CAAC6C,EAAE,CAACL,MAAM,CAAC;UACzCnB,WAAW,CAACuB,QAAQ,CAACE,IAAI,CAAC;UAC1BC,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;QACvB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD;UACA5B,WAAW,CAACgB,cAAc,CAACI,IAAI,CAAC;QAClC;MACF,CAAC;MACDE,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,oBACElC,OAAA;IAAQyC,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC7D1C,OAAA;MAAKyC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D1C,OAAA;QAAKyC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1C,OAAA;UACE2C,OAAO,EAAEzC,WAAY;UACrBuC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,eAEpE1C,OAAA,CAACR,IAAI;YAACiD,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGT/C,OAAA;UAAKyC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnC1C,OAAA;YAAKyC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB1C,OAAA;cAAKyC,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnF1C,OAAA,CAACN,MAAM;gBAAC+C,SAAS,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN/C,OAAA;cACEgD,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,WAAW;cACvBR,SAAS,EAAC;YAAwI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/C,OAAA;QAAKyC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1C1C,OAAA;UAAKyC,SAAS,EAAC,UAAU;UAACS,GAAG,EAAEnC,eAAgB;UAAA2B,QAAA,gBAC7C1C,OAAA;YACE2C,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDgC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBAEnE1C,OAAA,CAACP,IAAI;cAACgD,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B/C,OAAA;cAAMyC,SAAS,EAAC;YAAwD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,EAERtC,iBAAiB,iBAChBT,OAAA;YAAKyC,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnG1C,OAAA;cAAKyC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C1C,OAAA;gBAAIyC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACN/C,OAAA;cAAKyC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvC1C,OAAA;gBAAKyC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3C1C,OAAA;kBAAKyC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B1C,OAAA;oBAAKyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5B1C,OAAA;sBAAKyC,SAAS,EAAC;oBAAuC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACN/C,OAAA;oBAAKyC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB1C,OAAA;sBAAGyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAErC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ/C,OAAA;sBAAGyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/C,OAAA;gBAAKyC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3C1C,OAAA;kBAAKyC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B1C,OAAA;oBAAKyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5B1C,OAAA;sBAAKyC,SAAS,EAAC;oBAAyC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACN/C,OAAA;oBAAKyC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB1C,OAAA;sBAAGyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD/C,OAAA;sBAAGyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/C,OAAA;gBAAKyC,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClB1C,OAAA;kBAAQyC,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EAAC;gBAEjF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/C,OAAA;UAAKyC,SAAS,EAAC,UAAU;UAACS,GAAG,EAAEpC,WAAY;UAAA4B,QAAA,gBACzC1C,OAAA;YACE2C,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9CkC,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAExE1C,OAAA;cAAKyC,SAAS,EAAC,uDAAuD;cAAAC,QAAA,EACnE/B,QAAQ,aAARA,QAAQ,gBAAAP,cAAA,GAARO,QAAQ,CAAEwC,IAAI,cAAA/C,cAAA,eAAdA,cAAA,CAAgBgD,KAAK,gBACpBpD,OAAA;gBACEqD,GAAG,EAAE,iCAAiC1C,QAAQ,aAARA,QAAQ,wBAAAN,eAAA,GAARM,QAAQ,CAAEwC,IAAI,cAAA9C,eAAA,uBAAdA,eAAA,CAAgB+C,KAAK,EAAG;gBAC9DE,GAAG,EAAC,MAAM;gBACVb,SAAS,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,gBAEF/C,OAAA,CAACL,IAAI;gBAAC8C,SAAS,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN/C,OAAA;cAAKyC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC1C,OAAA;gBAAGyC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC7C,CAAA/B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4C,IAAI,KAAI;cAAY;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACJ/C,OAAA;gBAAGyC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjC,CAAA/B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,KAAK,KAAI;cAAmB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAERxC,YAAY,iBACXP,OAAA;YAAKyC,SAAS,EAAC,sFAAsF;YAAAC,QAAA,eACnG1C,OAAA;cAAKyC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB1C,OAAA;gBACE2C,OAAO,EAAEA,CAAA,KAAM;kBACb9B,QAAQ,CAAC,gBAAgB,CAAC;kBAC1BL,eAAe,CAAC,KAAK,CAAC;gBACxB,CAAE;gBACFiC,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBAEtF1C,OAAA,CAACL,IAAI;kBAAC8C,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/C,OAAA;gBACE2C,OAAO,EAAEA,CAAA,KAAM;kBACb9B,QAAQ,CAAC,iBAAiB,CAAC;kBAC3BL,eAAe,CAAC,KAAK,CAAC;gBACxB,CAAE;gBACFiC,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBAEtF1C,OAAA,CAACH,QAAQ;kBAAC4C,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/C,OAAA;gBAAIyC,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvB/C,OAAA;gBACE2C,OAAO,EAAE3B,YAAa;gBACtByB,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBAEnF1C,OAAA,CAACJ,MAAM;kBAAC6C,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC5C,EAAA,CAvMIF,MAAM;EAAA,QACSX,OAAO,EAITQ,WAAW;AAAA;AAAA2D,EAAA,GALxBxD,MAAM;AAyMZ,eAAeA,MAAM;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}