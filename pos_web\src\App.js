import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import Login from "./components/auth/Login";
import AdminLayout from "./components/layout/AdminLayout";
import Dashboard from "./pages/Dashboard";
import Profile from "./pages/profile";
import Products from "./pages/Products";
import ErrorBoundary from "./components/ErrorBoundary";
import "./App.css";

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <AuthProvider>
          <div className="App">
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />

              {/* Protected admin routes */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute>
                    <AdminLayout />
                  </ProtectedRoute>
                }
              >
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="products" element={<Products />} />
                <Route path="profile" element={<Profile />} />
                <Route
                  path="categories"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">
                        Categories Management
                      </h1>
                      <p className="text-gray-600 mt-2">
                        Category management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="orders"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">Orders Management</h1>
                      <p className="text-gray-600 mt-2">
                        Order management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="customers"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">
                        Customers Management
                      </h1>
                      <p className="text-gray-600 mt-2">
                        Customer management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="payments"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">
                        Payments Management
                      </h1>
                      <p className="text-gray-600 mt-2">
                        Payment management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="deliveries"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">
                        Deliveries Management
                      </h1>
                      <p className="text-gray-600 mt-2">
                        Delivery management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="reports/*"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">
                        Reports & Analytics
                      </h1>
                      <p className="text-gray-600 mt-2">
                        Reporting features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="notifications"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">Notifications</h1>
                      <p className="text-gray-600 mt-2">
                        Notification management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="users"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">User Management</h1>
                      <p className="text-gray-600 mt-2">
                        User management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="permissions"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">
                        Roles & Permissions
                      </h1>
                      <p className="text-gray-600 mt-2">
                        Permission management features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route
                  path="settings"
                  element={
                    <div className="p-6">
                      <h1 className="text-2xl font-bold">Settings</h1>
                      <p className="text-gray-600 mt-2">
                        Settings features coming soon...
                      </p>
                    </div>
                  }
                />
                <Route index element={<Navigate to="dashboard" replace />} />
              </Route>

              {/* Default redirect */}
              <Route
                path="/"
                element={<Navigate to="/admin/dashboard" replace />}
              />
              <Route
                path="*"
                element={<Navigate to="/admin/dashboard" replace />}
              />
            </Routes>
          </div>
        </AuthProvider>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
