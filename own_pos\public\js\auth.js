/**
 * Authentication Utility Library
 * Handles token-based authentication with roles and permissions
 * Uses sessionStorage for client-side storage
 */

class AuthManager {
    constructor() {
        this.tokenKey = 'auth_token';
        this.userKey = 'user_data';
        this.rolesKey = 'user_roles';
        this.permissionsKey = 'user_permissions';
        this.timestampKey = 'auth_timestamp';
    }

    /**
     * Store authentication data in sessionStorage
     */
    setAuth(data) {
        sessionStorage.setItem(this.tokenKey, data.token);
        sessionStorage.setItem(this.userKey, JSON.stringify(data.user));
        sessionStorage.setItem(this.rolesKey, JSON.stringify(data.roles || []));
        sessionStorage.setItem(this.permissionsKey, JSON.stringify(data.permissions || []));
        sessionStorage.setItem(this.timestampKey, Date.now().toString());
        
        // Dispatch custom event for auth state change
        window.dispatchEvent(new CustomEvent('authStateChanged', {
            detail: { authenticated: true, user: data.user }
        }));
    }

    /**
     * Get authentication token
     */
    getToken() {
        return sessionStorage.getItem(this.tokenKey);
    }

    /**
     * Get user data
     */
    getUser() {
        const userData = sessionStorage.getItem(this.userKey);
        return userData ? JSON.parse(userData) : null;
    }

    /**
     * Get user roles
     */
    getRoles() {
        const roles = sessionStorage.getItem(this.rolesKey);
        return roles ? JSON.parse(roles) : [];
    }

    /**
     * Get user permissions
     */
    getPermissions() {
        const permissions = sessionStorage.getItem(this.permissionsKey);
        return permissions ? JSON.parse(permissions) : [];
    }

    /**
     * Check if user has specific role
     */
    hasRole(role) {
        return this.getRoles().includes(role);
    }

    /**
     * Check if user has any of the specified roles
     */
    hasAnyRole(roles) {
        const userRoles = this.getRoles();
        return roles.some(role => userRoles.includes(role));
    }

    /**
     * Check if user has all specified roles
     */
    hasAllRoles(roles) {
        const userRoles = this.getRoles();
        return roles.every(role => userRoles.includes(role));
    }

    /**
     * Check if user has specific permission
     */
    hasPermission(permission) {
        return this.getPermissions().includes(permission);
    }

    /**
     * Check if user has any of the specified permissions
     */
    hasAnyPermission(permissions) {
        const userPermissions = this.getPermissions();
        return permissions.some(permission => userPermissions.includes(permission));
    }

    /**
     * Check if user has all specified permissions
     */
    hasAllPermissions(permissions) {
        const userPermissions = this.getPermissions();
        return permissions.every(permission => userPermissions.includes(permission));
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!this.getToken();
    }

    /**
     * Get authentication timestamp
     */
    getAuthTimestamp() {
        const timestamp = sessionStorage.getItem(this.timestampKey);
        return timestamp ? parseInt(timestamp) : null;
    }

    /**
     * Check if authentication is expired (optional expiry check)
     */
    isExpired(maxAgeMinutes = 480) { // 8 hours default
        const timestamp = this.getAuthTimestamp();
        if (!timestamp) return true;
        
        const now = Date.now();
        const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
        return (now - timestamp) > maxAge;
    }

    /**
     * Clear authentication data
     */
    logout() {
        sessionStorage.removeItem(this.tokenKey);
        sessionStorage.removeItem(this.userKey);
        sessionStorage.removeItem(this.rolesKey);
        sessionStorage.removeItem(this.permissionsKey);
        sessionStorage.removeItem(this.timestampKey);
        
        // Dispatch custom event for auth state change
        window.dispatchEvent(new CustomEvent('authStateChanged', {
            detail: { authenticated: false, user: null }
        }));
    }

    /**
     * Make authenticated API request
     */
    async apiRequest(url, options = {}) {
        const token = this.getToken();
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': this.getCSRFToken(),
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, mergedOptions);
            
            // Handle 401 Unauthorized
            if (response.status === 401) {
                this.logout();
                window.location.href = '/login';
                throw new Error('Authentication expired');
            }
            
            return response;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    /**
     * Get CSRF token from meta tag
     */
    getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : '';
    }

    /**
     * Login with credentials
     */
    async login(email, password, remember = false) {
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': this.getCSRFToken()
                },
                body: JSON.stringify({ email, password, remember })
            });

            const data = await response.json();

            if (data.success) {
                this.setAuth(data.data);
                return { success: true, data: data.data };
            } else {
                return { success: false, message: data.message, errors: data.errors };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'Network error occurred' };
        }
    }

    /**
     * Refresh user permissions from server
     */
    async refreshPermissions() {
        try {
            const response = await this.apiRequest('/api/refresh-permissions', {
                method: 'POST'
            });

            if (response.ok) {
                const data = await response.json();
                
                if (data.success) {
                    // Update sessionStorage with new permissions
                    sessionStorage.setItem(this.rolesKey, JSON.stringify(data.data.roles));
                    sessionStorage.setItem(this.permissionsKey, JSON.stringify(data.data.permissions));
                    
                    return { success: true, data: data.data };
                }
            }
            
            return { success: false, message: 'Failed to refresh permissions' };
        } catch (error) {
            console.error('Error refreshing permissions:', error);
            return { success: false, message: 'Network error occurred' };
        }
    }

    /**
     * Get current user info from server
     */
    async getCurrentUser() {
        try {
            const response = await this.apiRequest('/api/user');
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.success) {
                    // Update sessionStorage with fresh user data
                    sessionStorage.setItem(this.userKey, JSON.stringify(data.data.user));
                    sessionStorage.setItem(this.rolesKey, JSON.stringify(data.data.roles));
                    sessionStorage.setItem(this.permissionsKey, JSON.stringify(data.data.permissions));
                    
                    return { success: true, data: data.data };
                }
            }
            
            return { success: false, message: 'Failed to get user info' };
        } catch (error) {
            console.error('Error getting user info:', error);
            return { success: false, message: 'Network error occurred' };
        }
    }
}

// Create global instance
window.Auth = new AuthManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
