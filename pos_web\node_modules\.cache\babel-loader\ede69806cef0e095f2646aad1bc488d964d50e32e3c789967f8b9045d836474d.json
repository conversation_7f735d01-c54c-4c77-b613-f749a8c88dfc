{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0\",\n  key: \"vqan6v\"\n}], [\"path\", {\n  d: \"M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0\",\n  key: \"wdjd8o\"\n}]];\nconst Section = createLucideIcon(\"section\", __iconNode);\nexport { __iconNode, Section as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Section", "createLucideIcon"], "sources": ["E:\\Developer\\Pos_system\\pos_web\\node_modules\\lucide-react\\src\\icons\\section.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0', key: 'vqan6v' }],\n  ['path', { d: 'M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0', key: 'wdjd8o' }],\n];\n\n/**\n * @component @name Section\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgNWE0IDMgMCAwIDAtOCAwYzAgNCA4IDMgOCA3YTQgMyAwIDAgMS04IDAiIC8+CiAgPHBhdGggZD0iTTggMTlhNCAzIDAgMCAwIDggMGMwLTQtOC0zLTgtN2E0IDMgMCAwIDEgOCAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/section\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Section = createLucideIcon('section', __iconNode);\n\nexport default Section;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAU,GAChF;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}