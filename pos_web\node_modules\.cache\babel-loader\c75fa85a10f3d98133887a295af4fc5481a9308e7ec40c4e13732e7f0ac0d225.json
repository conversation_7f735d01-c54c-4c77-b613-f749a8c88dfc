{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\pages\\\\LowStockProducts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { productsAPI } from '../services/api';\nimport { AlertTriangle, Package, Edit, Plus } from 'lucide-react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LowStockProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchLowStockProducts();\n  }, []);\n  const fetchLowStockProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      // Filter products with stock less than 10\n      const lowStockProducts = (response.data.data || []).filter(product => (product.stock || 0) < 10 && (product.stock || 0) > 0);\n      setProducts(lowStockProducts);\n    } catch (error) {\n      console.error('Error fetching low stock products:', error);\n      // Use mock low stock data if API fails\n      setProducts([{\n        id: 2,\n        name: 'Organic Tea Blend',\n        category: 'Beverages',\n        price: 18.50,\n        stock: 5,\n        status: 'active',\n        image: '/api/placeholder/100/100'\n      }, {\n        id: 4,\n        name: 'Special Blend Coffee',\n        category: 'Beverages',\n        price: 22.99,\n        stock: 3,\n        status: 'active',\n        image: '/api/placeholder/100/100'\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStockStatus = stock => {\n    const stockValue = stock || 0;\n    if (stockValue === 0) return {\n      text: 'Out of Stock',\n      color: 'text-red-600 bg-red-100'\n    };\n    if (stockValue < 10) return {\n      text: 'Low Stock',\n      color: 'text-yellow-600 bg-yellow-100'\n    };\n    return {\n      text: 'In Stock',\n      color: 'text-green-600 bg-green-100'\n    };\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            className: \"w-6 h-6 text-yellow-500 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), \"Low Stock Products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Products that need restocking (less than 10 items)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/admin/products\",\n        className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n        children: [/*#__PURE__*/_jsxDEV(Package, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), \"View All Products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n          className: \"w-5 h-5 text-yellow-600 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-yellow-800\",\n            children: [products.length, \" product\", products.length !== 1 ? 's' : '', \" running low on stock\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-yellow-700 mt-1\",\n            children: \"Consider restocking these items to avoid stockouts.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), products.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: products.map(product => {\n              const stockStatus = getStockStatus(product.stock);\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 h-10 w-10\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(Package, {\n                          className: \"w-5 h-5 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 137,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 136,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: product.name || 'Unnamed Product'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: product.category || 'Uncategorized'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: [\"$\", product.price || '0.00']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-900 mr-2\",\n                      children: product.stock || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                      className: \"w-4 h-4 text-yellow-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`,\n                    children: stockStatus.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-green-600 hover:text-green-900\",\n                    children: /*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 23\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(Package, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No low stock products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"All products are well stocked!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(LowStockProducts, \"KWid68LpBxbFhyja5dauhUIHvyY=\");\n_c = LowStockProducts;\nexport default LowStockProducts;\nvar _c;\n$RefreshReg$(_c, \"LowStockProducts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "productsAPI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package", "Edit", "Plus", "Link", "jsxDEV", "_jsxDEV", "LowStockProducts", "_s", "products", "setProducts", "loading", "setLoading", "fetchLowStockProducts", "response", "getAll", "lowStockProducts", "data", "filter", "product", "stock", "error", "console", "id", "name", "category", "price", "status", "image", "getStockStatus", "stockValue", "text", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "length", "map", "stockStatus", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/pages/LowStockProducts.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { productsAPI } from '../services/api';\nimport { AlertTriangle, Package, Edit, Plus } from 'lucide-react';\nimport { Link } from 'react-router-dom';\n\nconst LowStockProducts = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchLowStockProducts();\n  }, []);\n\n  const fetchLowStockProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      // Filter products with stock less than 10\n      const lowStockProducts = (response.data.data || []).filter(product => \n        (product.stock || 0) < 10 && (product.stock || 0) > 0\n      );\n      setProducts(lowStockProducts);\n    } catch (error) {\n      console.error('Error fetching low stock products:', error);\n      // Use mock low stock data if API fails\n      setProducts([\n        {\n          id: 2,\n          name: 'Organic Tea Blend',\n          category: 'Beverages',\n          price: 18.50,\n          stock: 5,\n          status: 'active',\n          image: '/api/placeholder/100/100'\n        },\n        {\n          id: 4,\n          name: 'Special Blend Coffee',\n          category: 'Beverages',\n          price: 22.99,\n          stock: 3,\n          status: 'active',\n          image: '/api/placeholder/100/100'\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStockStatus = (stock) => {\n    const stockValue = stock || 0;\n    if (stockValue === 0) return { text: 'Out of Stock', color: 'text-red-600 bg-red-100' };\n    if (stockValue < 10) return { text: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' };\n    return { text: 'In Stock', color: 'text-green-600 bg-green-100' };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n            <AlertTriangle className=\"w-6 h-6 text-yellow-500 mr-2\" />\n            Low Stock Products\n          </h1>\n          <p className=\"text-gray-600\">Products that need restocking (less than 10 items)</p>\n        </div>\n        <Link\n          to=\"/admin/products\"\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n        >\n          <Package className=\"w-4 h-4 mr-2\" />\n          View All Products\n        </Link>\n      </div>\n\n      {/* Alert Banner */}\n      <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n        <div className=\"flex items-center\">\n          <AlertTriangle className=\"w-5 h-5 text-yellow-600 mr-2\" />\n          <div>\n            <h3 className=\"text-sm font-medium text-yellow-800\">\n              {products.length} product{products.length !== 1 ? 's' : ''} running low on stock\n            </h3>\n            <p className=\"text-sm text-yellow-700 mt-1\">\n              Consider restocking these items to avoid stockouts.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Products List */}\n      {products.length > 0 ? (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Product\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Category\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Price\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Stock\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {products.map((product) => {\n                  const stockStatus = getStockStatus(product.stock);\n                  return (\n                    <tr key={product.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center\">\n                              <Package className=\"w-5 h-5 text-gray-400\" />\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {product.name || 'Unnamed Product'}\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {product.category || 'Uncategorized'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        ${product.price || '0.00'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <span className=\"text-sm font-medium text-gray-900 mr-2\">\n                            {product.stock || 0}\n                          </span>\n                          <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>\n                          {stockStatus.text}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <button className=\"text-blue-600 hover:text-blue-900 mr-3\">\n                          <Edit className=\"w-4 h-4\" />\n                        </button>\n                        <button className=\"text-green-600 hover:text-green-900\">\n                          <Plus className=\"w-4 h-4\" />\n                        </button>\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      ) : (\n        <div className=\"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200\">\n          <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No low stock products</h3>\n          <p className=\"text-gray-600\">All products are well stocked!</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default LowStockProducts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,aAAa,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AACjE,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACde,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMf,WAAW,CAACgB,MAAM,CAAC,CAAC;MAC3C;MACA,MAAMC,gBAAgB,GAAG,CAACF,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,EAAEC,MAAM,CAACC,OAAO,IAChE,CAACA,OAAO,CAACC,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAACD,OAAO,CAACC,KAAK,IAAI,CAAC,IAAI,CACtD,CAAC;MACDV,WAAW,CAACM,gBAAgB,CAAC;IAC/B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D;MACAX,WAAW,CAAC,CACV;QACEa,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,mBAAmB;QACzBC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,KAAK;QACZN,KAAK,EAAE,CAAC;QACRO,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,KAAK;QACZN,KAAK,EAAE,CAAC;QACRO,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,cAAc,GAAIT,KAAK,IAAK;IAChC,MAAMU,UAAU,GAAGV,KAAK,IAAI,CAAC;IAC7B,IAAIU,UAAU,KAAK,CAAC,EAAE,OAAO;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAA0B,CAAC;IACvF,IAAIF,UAAU,GAAG,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAgC,CAAC;IACzF,OAAO;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAA8B,CAAC;EACnE,CAAC;EAED,IAAIrB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK2B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD5B,OAAA;QAAK2B,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAK2B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5B,OAAA;MAAK2B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5B,OAAA;QAAA4B,QAAA,gBACE5B,OAAA;UAAI2B,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAChE5B,OAAA,CAACN,aAAa;YAACiC,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhC,OAAA;UAAG2B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eACNhC,OAAA,CAACF,IAAI;QACHmC,EAAE,EAAC,iBAAiB;QACpBN,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAE3F5B,OAAA,CAACL,OAAO;UAACgC,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAEtC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnE5B,OAAA;QAAK2B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5B,OAAA,CAACN,aAAa;UAACiC,SAAS,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DhC,OAAA;UAAA4B,QAAA,gBACE5B,OAAA;YAAI2B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAChDzB,QAAQ,CAAC+B,MAAM,EAAC,UAAQ,EAAC/B,QAAQ,CAAC+B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,uBAC7D;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhC,OAAA;YAAG2B,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7B,QAAQ,CAAC+B,MAAM,GAAG,CAAC,gBAClBlC,OAAA;MAAK2B,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACnF5B,OAAA;QAAK2B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B5B,OAAA;UAAO2B,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpD5B,OAAA;YAAO2B,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3B5B,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAI2B,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAI2B,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAI2B,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAI2B,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAI2B,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAI2B,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhC,OAAA;YAAO2B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDzB,QAAQ,CAACgC,GAAG,CAAEtB,OAAO,IAAK;cACzB,MAAMuB,WAAW,GAAGb,cAAc,CAACV,OAAO,CAACC,KAAK,CAAC;cACjD,oBACEd,OAAA;gBAAqB2B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/C5B,OAAA;kBAAI2B,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC5B,OAAA;oBAAK2B,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC5B,OAAA;sBAAK2B,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACtC5B,OAAA;wBAAK2B,SAAS,EAAC,mEAAmE;wBAAAC,QAAA,eAChF5B,OAAA,CAACL,OAAO;0BAACgC,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhC,OAAA;sBAAK2B,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB5B,OAAA;wBAAK2B,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC/Cf,OAAO,CAACK,IAAI,IAAI;sBAAiB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLhC,OAAA;kBAAI2B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9Df,OAAO,CAACM,QAAQ,IAAI;gBAAe;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACLhC,OAAA;kBAAI2B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,GAC/D,EAACf,OAAO,CAACO,KAAK,IAAI,MAAM;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACLhC,OAAA;kBAAI2B,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC5B,OAAA;oBAAK2B,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC5B,OAAA;sBAAM2B,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,EACrDf,OAAO,CAACC,KAAK,IAAI;oBAAC;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACPhC,OAAA,CAACN,aAAa;sBAACiC,SAAS,EAAC;oBAAyB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLhC,OAAA;kBAAI2B,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC5B,OAAA;oBAAM2B,SAAS,EAAE,4DAA4DS,WAAW,CAACV,KAAK,EAAG;oBAAAE,QAAA,EAC9FQ,WAAW,CAACX;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhC,OAAA;kBAAI2B,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D5B,OAAA;oBAAQ2B,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eACxD5B,OAAA,CAACJ,IAAI;sBAAC+B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACThC,OAAA;oBAAQ2B,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eACrD5B,OAAA,CAACH,IAAI;sBAAC8B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAzCEnB,OAAO,CAACI,EAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0Cf,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENhC,OAAA;MAAK2B,SAAS,EAAC,wEAAwE;MAAAC,QAAA,gBACrF5B,OAAA,CAACL,OAAO;QAACgC,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5DhC,OAAA;QAAI2B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjFhC,OAAA;QAAG2B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAxLID,gBAAgB;AAAAoC,EAAA,GAAhBpC,gBAAgB;AA0LtB,eAAeA,gBAAgB;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}