{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\pages\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { productsAPI } from '../services/api';\nimport { Plus, Search, Edit, Trash2, Package, AlertTriangle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      setProducts(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Use mock data if API fails\n      setProducts([{\n        id: 1,\n        name: 'Premium Coffee Beans',\n        category: 'Beverages',\n        price: 24.99,\n        stock: 150,\n        status: 'active',\n        image: '/api/placeholder/100/100'\n      }, {\n        id: 2,\n        name: 'Organic Tea Blend',\n        category: 'Beverages',\n        price: 18.50,\n        stock: 5,\n        status: 'active',\n        image: '/api/placeholder/100/100'\n      }, {\n        id: 3,\n        name: 'Artisan Pastry',\n        category: 'Food',\n        price: 8.99,\n        stock: 0,\n        status: 'inactive',\n        image: '/api/placeholder/100/100'\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredProducts = products.filter(product => product.name && product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.category && product.category.toLowerCase().includes(searchTerm.toLowerCase()));\n  const getStockStatus = stock => {\n    if (stock === 0) return {\n      text: 'Out of Stock',\n      color: 'text-red-600 bg-red-100'\n    };\n    if (stock < 10) return {\n      text: 'Low Stock',\n      color: 'text-yellow-600 bg-yellow-100'\n    };\n    return {\n      text: 'In Stock',\n      color: 'text-green-600 bg-green-100'\n    };\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your product inventory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddModal(true),\n        className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), \"Add Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"All Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Beverages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Food\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Accessories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"All Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Inactive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: filteredProducts.map(product => {\n        const stockStatus = getStockStatus(product.stock);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-w-1 aspect-h-1 bg-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full h-48 bg-gray-100 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Package, {\n                className: \"w-12 h-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 truncate\",\n                children: product.name || 'Unnamed Product'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setEditingProduct(product),\n                  className: \"p-1 text-gray-400 hover:text-blue-600\",\n                  children: /*#__PURE__*/_jsxDEV(Edit, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-gray-400 hover:text-red-600\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-2\",\n              children: product.category || 'Uncategorized'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: [\"$\", product.price || '0.00']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`,\n                children: stockStatus.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Stock: \", product.stock || 0]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), (product.stock || 0) < 10 && (product.stock || 0) > 0 && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"w-4 h-4 text-yellow-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), filteredProducts.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Package, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No products found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Try adjusting your search or add a new product.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this), showAddModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4\",\n          children: \"Add New Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Product form will be implemented here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddModal(false),\n            className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n            children: \"Save Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"kLlX5++oSx1s1jFlK5uOpedi1tQ=\");\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "productsAPI", "Plus", "Search", "Edit", "Trash2", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Products", "_s", "products", "setProducts", "loading", "setLoading", "searchTerm", "setSearchTerm", "showAddModal", "setShowAddModal", "editingProduct", "setEditingProduct", "fetchProducts", "response", "getAll", "data", "error", "console", "id", "name", "category", "price", "stock", "status", "image", "filteredProducts", "filter", "product", "toLowerCase", "includes", "getStockStatus", "text", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "stockStatus", "length", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/pages/Products.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { productsAPI } from '../services/api';\nimport { Plus, Search, Edit, Trash2, Package, AlertTriangle } from 'lucide-react';\n\nconst Products = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      setProducts(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Use mock data if API fails\n      setProducts([\n        {\n          id: 1,\n          name: 'Premium Coffee Beans',\n          category: 'Beverages',\n          price: 24.99,\n          stock: 150,\n          status: 'active',\n          image: '/api/placeholder/100/100'\n        },\n        {\n          id: 2,\n          name: 'Organic Tea Blend',\n          category: 'Beverages',\n          price: 18.50,\n          stock: 5,\n          status: 'active',\n          image: '/api/placeholder/100/100'\n        },\n        {\n          id: 3,\n          name: 'Artisan Pastry',\n          category: 'Food',\n          price: 8.99,\n          stock: 0,\n          status: 'inactive',\n          image: '/api/placeholder/100/100'\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredProducts = products.filter(product =>\n    (product.name && product.name.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (product.category && product.category.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const getStockStatus = (stock) => {\n    if (stock === 0) return { text: 'Out of Stock', color: 'text-red-600 bg-red-100' };\n    if (stock < 10) return { text: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' };\n    return { text: 'In Stock', color: 'text-green-600 bg-green-100' };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Products</h1>\n          <p className=\"text-gray-600\">Manage your product inventory</p>\n        </div>\n        <button\n          onClick={() => setShowAddModal(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n        >\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Add Product\n        </button>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n          <select className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\">\n            <option>All Categories</option>\n            <option>Beverages</option>\n            <option>Food</option>\n            <option>Accessories</option>\n          </select>\n          <select className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\">\n            <option>All Status</option>\n            <option>Active</option>\n            <option>Inactive</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {filteredProducts.map((product) => {\n          const stockStatus = getStockStatus(product.stock);\n          return (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n              <div className=\"aspect-w-1 aspect-h-1 bg-gray-200\">\n                <div className=\"w-full h-48 bg-gray-100 flex items-center justify-center\">\n                  <Package className=\"w-12 h-12 text-gray-400\" />\n                </div>\n              </div>\n              \n              <div className=\"p-4\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n                    {product.name || 'Unnamed Product'}\n                  </h3>\n                  <div className=\"flex space-x-1\">\n                    <button\n                      onClick={() => setEditingProduct(product)}\n                      className=\"p-1 text-gray-400 hover:text-blue-600\"\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                    </button>\n                    <button className=\"p-1 text-gray-400 hover:text-red-600\">\n                      <Trash2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n                \n                <p className=\"text-sm text-gray-600 mb-2\">{product.category || 'Uncategorized'}</p>\n\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"text-xl font-bold text-gray-900\">\n                    ${product.price || '0.00'}\n                  </span>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>\n                    {stockStatus.text}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                  <span>Stock: {product.stock || 0}</span>\n                  {(product.stock || 0) < 10 && (product.stock || 0) > 0 && (\n                    <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />\n                  )}\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products found</h3>\n          <p className=\"text-gray-600\">Try adjusting your search or add a new product.</p>\n        </div>\n      )}\n\n      {/* Add/Edit Modal would go here */}\n      {showAddModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <h2 className=\"text-xl font-bold mb-4\">Add New Product</h2>\n            <p className=\"text-gray-600 mb-4\">Product form will be implemented here...</p>\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setShowAddModal(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\">\n                Save Product\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElF,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACdsB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMtB,WAAW,CAACuB,MAAM,CAAC,CAAC;MAC3CX,WAAW,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACAb,WAAW,CAAC,CACV;QACEe,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,mBAAmB;QACzBC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,gBAAgB;QACtBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE;MACT,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,gBAAgB,GAAGvB,QAAQ,CAACwB,MAAM,CAACC,OAAO,IAC7CA,OAAO,CAACR,IAAI,IAAIQ,OAAO,CAACR,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC7ED,OAAO,CAACP,QAAQ,IAAIO,OAAO,CAACP,QAAQ,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CACvF,CAAC;EAED,MAAME,cAAc,GAAIR,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;MAAES,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAA0B,CAAC;IAClF,IAAIV,KAAK,GAAG,EAAE,EAAE,OAAO;MAAES,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAgC,CAAC;IACpF,OAAO;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAA8B,CAAC;EACnE,CAAC;EAED,IAAI5B,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDnC,OAAA;QAAKkC,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACEvC,OAAA;IAAKkC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnC,OAAA;MAAKkC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnC,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAIkC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DvC,OAAA;UAAGkC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACNvC,OAAA;QACEwC,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAAC,IAAI,CAAE;QACrCwB,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAE3FnC,OAAA,CAACP,IAAI;UAACyC,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEnC,OAAA;QAAKkC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnC,OAAA;UAAKkC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnC,OAAA;YAAKkC,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFnC,OAAA,CAACN,MAAM;cAACwC,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNvC,OAAA;YACEyC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAEpC,UAAW;YAClBqC,QAAQ,EAAGC,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAA0I;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvC,OAAA;UAAQkC,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBACjHnC,OAAA;YAAAmC,QAAA,EAAQ;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/BvC,OAAA;YAAAmC,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1BvC,OAAA;YAAAmC,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrBvC,OAAA;YAAAmC,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACTvC,OAAA;UAAQkC,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBACjHnC,OAAA;YAAAmC,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3BvC,OAAA;YAAAmC,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvBvC,OAAA;YAAAmC,QAAA,EAAQ;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFT,gBAAgB,CAACqB,GAAG,CAAEnB,OAAO,IAAK;QACjC,MAAMoB,WAAW,GAAGjB,cAAc,CAACH,OAAO,CAACL,KAAK,CAAC;QACjD,oBACEvB,OAAA;UAAsBkC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACpGnC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDnC,OAAA;cAAKkC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,eACvEnC,OAAA,CAACH,OAAO;gBAACqC,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBnC,OAAA;cAAKkC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDnC,OAAA;gBAAIkC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzDP,OAAO,CAACR,IAAI,IAAI;cAAiB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACLvC,OAAA;gBAAKkC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BnC,OAAA;kBACEwC,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAACgB,OAAO,CAAE;kBAC1CM,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,eAEjDnC,OAAA,CAACL,IAAI;oBAACuC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTvC,OAAA;kBAAQkC,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,eACtDnC,OAAA,CAACJ,MAAM;oBAACsC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvC,OAAA;cAAGkC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEP,OAAO,CAACP,QAAQ,IAAI;YAAe;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEnFvC,OAAA;cAAKkC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnC,OAAA;gBAAMkC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAC,GAC/C,EAACP,OAAO,CAACN,KAAK,IAAI,MAAM;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACPvC,OAAA;gBAAMkC,SAAS,EAAE,8CAA8Cc,WAAW,CAACf,KAAK,EAAG;gBAAAE,QAAA,EAChFa,WAAW,CAAChB;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENvC,OAAA;cAAKkC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtEnC,OAAA;gBAAAmC,QAAA,GAAM,SAAO,EAACP,OAAO,CAACL,KAAK,IAAI,CAAC;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACvC,CAACX,OAAO,CAACL,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAACK,OAAO,CAACL,KAAK,IAAI,CAAC,IAAI,CAAC,iBACpDvB,OAAA,CAACF,aAAa;gBAACoC,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA1CEX,OAAO,CAACT,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Cf,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELb,gBAAgB,CAACuB,MAAM,KAAK,CAAC,iBAC5BjD,OAAA;MAAKkC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCnC,OAAA,CAACH,OAAO;QAACqC,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5DvC,OAAA;QAAIkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EvC,OAAA;QAAGkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACN,EAGA9B,YAAY,iBACXT,OAAA;MAAKkC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFnC,OAAA;QAAKkC,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDnC,OAAA;UAAIkC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DvC,OAAA;UAAGkC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9EvC,OAAA;UAAKkC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCnC,OAAA;YACEwC,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAAC,KAAK,CAAE;YACtCwB,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACvF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvC,OAAA;YAAQkC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAElF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrC,EAAA,CAzMID,QAAQ;AAAAiD,EAAA,GAARjD,QAAQ;AA2Md,eAAeA,QAAQ;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}