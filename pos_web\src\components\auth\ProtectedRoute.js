import { Navigate } from "react-router-dom";

const ProtectedRoute = ({ children, permissions = [] }) => {
  try {
    const userString = sessionStorage.getItem("user");
    if (!userString) {
      console.warn("User data not found. Redirecting to login.");
      return <Navigate to="/login" replace />;
    }
    const userData = JSON.parse(userString);
    const { token, user, permissions: userPermissions } = userData;
    if (!token || !user) {
      console.warn("User data not found. Redirecting to login.");
      sessionStorage.removeItem("user");
      return <Navigate to="/login" replace />;
    }
    // Handle permissions from the correct location in the response
    const userPermission = (userPermissions || [])
      .map((permission) =>
        typeof permission === "string"
          ? permission.toLowerCase()
          : permission.name?.toLowerCase()
      )
      .filter(Boolean);
    const hasPermission = permissions.every((prem) =>
      userPermission.includes(prem.toLowerCase())
    );
    if (!hasPermission) {
      console.warn(
        "User does not have required permissions. Redirecting to login."
      );
      return <Navigate to="/login" replace />;
    }
    return children;
  } catch (error) {
    console.error("Error checking permissions:", error);
    sessionStorage.removeItem("user");
    return <Navigate to="/login" replace />;
  }
};

export default ProtectedRoute;
