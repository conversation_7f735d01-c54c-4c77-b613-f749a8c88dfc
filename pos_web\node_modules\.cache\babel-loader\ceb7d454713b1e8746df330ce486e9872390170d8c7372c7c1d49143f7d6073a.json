{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\pages\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { productsAPI } from '../services/api';\nimport { Plus, Search, Edit, Trash2, Package, AlertTriangle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      setProducts(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Use mock data if API fails\n      setProducts([{\n        id: 1,\n        name: 'Premium Coffee Beans',\n        category: 'Beverages',\n        price: 24.99,\n        stock: 150,\n        status: 'active',\n        image: '/api/placeholder/100/100'\n      }, {\n        id: 2,\n        name: 'Organic Tea Blend',\n        category: 'Beverages',\n        price: 18.50,\n        stock: 5,\n        status: 'active',\n        image: '/api/placeholder/100/100'\n      }, {\n        id: 3,\n        name: 'Artisan Pastry',\n        category: 'Food',\n        price: 8.99,\n        stock: 0,\n        status: 'inactive',\n        image: '/api/placeholder/100/100'\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredProducts = products.filter(product => {\n    const productName = product.name || '';\n    const productCategory = product.category || '';\n    const searchLower = searchTerm.toLowerCase();\n    return typeof productName === 'string' && productName.toLowerCase().includes(searchLower) || typeof productCategory === 'string' && productCategory.toLowerCase().includes(searchLower);\n  });\n  const getStockStatus = stock => {\n    const stockValue = stock || 0;\n    if (stockValue === 0) return {\n      text: 'Out of Stock',\n      color: 'text-red-600 bg-red-100'\n    };\n    if (stockValue < 10) return {\n      text: 'Low Stock',\n      color: 'text-yellow-600 bg-yellow-100'\n    };\n    return {\n      text: 'In Stock',\n      color: 'text-green-600 bg-green-100'\n    };\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your product inventory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddModal(true),\n        className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), \"Add Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"All Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Beverages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Food\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Accessories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"All Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Inactive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: filteredProducts.map(product => {\n        const stockStatus = getStockStatus(product.stock);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-w-1 aspect-h-1 bg-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full h-48 bg-gray-100 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Package, {\n                className: \"w-12 h-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 truncate\",\n                children: product.name || 'Unnamed Product'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setEditingProduct(product),\n                  className: \"p-1 text-gray-400 hover:text-blue-600\",\n                  children: /*#__PURE__*/_jsxDEV(Edit, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-gray-400 hover:text-red-600\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-2\",\n              children: product.category || 'Uncategorized'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: [\"$\", product.price || '0.00']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`,\n                children: stockStatus.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Stock: \", product.stock || 0]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), (product.stock || 0) < 10 && (product.stock || 0) > 0 && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"w-4 h-4 text-yellow-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), filteredProducts.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Package, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No products found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Try adjusting your search or add a new product.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this), showAddModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4\",\n          children: \"Add New Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Product form will be implemented here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddModal(false),\n            className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n            children: \"Save Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"kLlX5++oSx1s1jFlK5uOpedi1tQ=\");\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "productsAPI", "Plus", "Search", "Edit", "Trash2", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Products", "_s", "products", "setProducts", "loading", "setLoading", "searchTerm", "setSearchTerm", "showAddModal", "setShowAddModal", "editingProduct", "setEditingProduct", "fetchProducts", "response", "getAll", "data", "error", "console", "id", "name", "category", "price", "stock", "status", "image", "filteredProducts", "filter", "product", "productName", "productCategory", "searchLower", "toLowerCase", "includes", "getStockStatus", "stockValue", "text", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "stockStatus", "length", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/pages/Products.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { productsAPI } from '../services/api';\nimport { Plus, Search, Edit, Trash2, Package, AlertTriangle } from 'lucide-react';\n\nconst Products = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      setProducts(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Use mock data if API fails\n      setProducts([\n        {\n          id: 1,\n          name: 'Premium Coffee Beans',\n          category: 'Beverages',\n          price: 24.99,\n          stock: 150,\n          status: 'active',\n          image: '/api/placeholder/100/100'\n        },\n        {\n          id: 2,\n          name: 'Organic Tea Blend',\n          category: 'Beverages',\n          price: 18.50,\n          stock: 5,\n          status: 'active',\n          image: '/api/placeholder/100/100'\n        },\n        {\n          id: 3,\n          name: 'Artisan Pastry',\n          category: 'Food',\n          price: 8.99,\n          stock: 0,\n          status: 'inactive',\n          image: '/api/placeholder/100/100'\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredProducts = products.filter(product => {\n    const productName = product.name || '';\n    const productCategory = product.category || '';\n    const searchLower = searchTerm.toLowerCase();\n\n    return (\n      (typeof productName === 'string' && productName.toLowerCase().includes(searchLower)) ||\n      (typeof productCategory === 'string' && productCategory.toLowerCase().includes(searchLower))\n    );\n  });\n\n  const getStockStatus = (stock) => {\n    const stockValue = stock || 0;\n    if (stockValue === 0) return { text: 'Out of Stock', color: 'text-red-600 bg-red-100' };\n    if (stockValue < 10) return { text: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' };\n    return { text: 'In Stock', color: 'text-green-600 bg-green-100' };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Products</h1>\n          <p className=\"text-gray-600\">Manage your product inventory</p>\n        </div>\n        <button\n          onClick={() => setShowAddModal(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n        >\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Add Product\n        </button>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n          <select className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\">\n            <option>All Categories</option>\n            <option>Beverages</option>\n            <option>Food</option>\n            <option>Accessories</option>\n          </select>\n          <select className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\">\n            <option>All Status</option>\n            <option>Active</option>\n            <option>Inactive</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {filteredProducts.map((product) => {\n          const stockStatus = getStockStatus(product.stock);\n          return (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n              <div className=\"aspect-w-1 aspect-h-1 bg-gray-200\">\n                <div className=\"w-full h-48 bg-gray-100 flex items-center justify-center\">\n                  <Package className=\"w-12 h-12 text-gray-400\" />\n                </div>\n              </div>\n\n              <div className=\"p-4\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n                    {product.name || 'Unnamed Product'}\n                  </h3>\n                  <div className=\"flex space-x-1\">\n                    <button\n                      onClick={() => setEditingProduct(product)}\n                      className=\"p-1 text-gray-400 hover:text-blue-600\"\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                    </button>\n                    <button className=\"p-1 text-gray-400 hover:text-red-600\">\n                      <Trash2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                <p className=\"text-sm text-gray-600 mb-2\">{product.category || 'Uncategorized'}</p>\n\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"text-xl font-bold text-gray-900\">\n                    ${product.price || '0.00'}\n                  </span>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>\n                    {stockStatus.text}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                  <span>Stock: {product.stock || 0}</span>\n                  {(product.stock || 0) < 10 && (product.stock || 0) > 0 && (\n                    <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />\n                  )}\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products found</h3>\n          <p className=\"text-gray-600\">Try adjusting your search or add a new product.</p>\n        </div>\n      )}\n\n      {/* Add/Edit Modal would go here */}\n      {showAddModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <h2 className=\"text-xl font-bold mb-4\">Add New Product</h2>\n            <p className=\"text-gray-600 mb-4\">Product form will be implemented here...</p>\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setShowAddModal(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\">\n                Save Product\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElF,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACdsB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMtB,WAAW,CAACuB,MAAM,CAAC,CAAC;MAC3CX,WAAW,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACAb,WAAW,CAAC,CACV;QACEe,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,mBAAmB;QACzBC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,gBAAgB;QACtBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE;MACT,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,gBAAgB,GAAGvB,QAAQ,CAACwB,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,WAAW,GAAGD,OAAO,CAACR,IAAI,IAAI,EAAE;IACtC,MAAMU,eAAe,GAAGF,OAAO,CAACP,QAAQ,IAAI,EAAE;IAC9C,MAAMU,WAAW,GAAGxB,UAAU,CAACyB,WAAW,CAAC,CAAC;IAE5C,OACG,OAAOH,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAClF,OAAOD,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE;EAEhG,CAAC,CAAC;EAEF,MAAMG,cAAc,GAAIX,KAAK,IAAK;IAChC,MAAMY,UAAU,GAAGZ,KAAK,IAAI,CAAC;IAC7B,IAAIY,UAAU,KAAK,CAAC,EAAE,OAAO;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAA0B,CAAC;IACvF,IAAIF,UAAU,GAAG,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAgC,CAAC;IACzF,OAAO;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAA8B,CAAC;EACnE,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKsC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDvC,OAAA;QAAKsC,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE3C,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvC,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDvC,OAAA;QAAAuC,QAAA,gBACEvC,OAAA;UAAIsC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9D3C,OAAA;UAAGsC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACN3C,OAAA;QACE4C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,IAAI,CAAE;QACrC4B,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAE3FvC,OAAA,CAACP,IAAI;UAAC6C,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEvC,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CvC,OAAA;UAAKsC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvC,OAAA;YAAKsC,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFvC,OAAA,CAACN,MAAM;cAAC4C,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN3C,OAAA;YACE6C,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAExC,UAAW;YAClByC,QAAQ,EAAGC,CAAC,IAAKzC,aAAa,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAA0I;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3C,OAAA;UAAQsC,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBACjHvC,OAAA;YAAAuC,QAAA,EAAQ;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/B3C,OAAA;YAAAuC,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1B3C,OAAA;YAAAuC,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrB3C,OAAA;YAAAuC,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACT3C,OAAA;UAAQsC,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBACjHvC,OAAA;YAAAuC,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3B3C,OAAA;YAAAuC,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvB3C,OAAA;YAAAuC,QAAA,EAAQ;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFb,gBAAgB,CAACyB,GAAG,CAAEvB,OAAO,IAAK;QACjC,MAAMwB,WAAW,GAAGlB,cAAc,CAACN,OAAO,CAACL,KAAK,CAAC;QACjD,oBACEvB,OAAA;UAAsBsC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACpGvC,OAAA;YAAKsC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDvC,OAAA;cAAKsC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,eACvEvC,OAAA,CAACH,OAAO;gBAACyC,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBvC,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDvC,OAAA;gBAAIsC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzDX,OAAO,CAACR,IAAI,IAAI;cAAiB;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACL3C,OAAA;gBAAKsC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvC,OAAA;kBACE4C,OAAO,EAAEA,CAAA,KAAMhC,iBAAiB,CAACgB,OAAO,CAAE;kBAC1CU,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,eAEjDvC,OAAA,CAACL,IAAI;oBAAC2C,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACT3C,OAAA;kBAAQsC,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,eACtDvC,OAAA,CAACJ,MAAM;oBAAC0C,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3C,OAAA;cAAGsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEX,OAAO,CAACP,QAAQ,IAAI;YAAe;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEnF3C,OAAA;cAAKsC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDvC,OAAA;gBAAMsC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAC,GAC/C,EAACX,OAAO,CAACN,KAAK,IAAI,MAAM;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACP3C,OAAA;gBAAMsC,SAAS,EAAE,8CAA8Cc,WAAW,CAACf,KAAK,EAAG;gBAAAE,QAAA,EAChFa,WAAW,CAAChB;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN3C,OAAA;cAAKsC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtEvC,OAAA;gBAAAuC,QAAA,GAAM,SAAO,EAACX,OAAO,CAACL,KAAK,IAAI,CAAC;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACvC,CAACf,OAAO,CAACL,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAACK,OAAO,CAACL,KAAK,IAAI,CAAC,IAAI,CAAC,iBACpDvB,OAAA,CAACF,aAAa;gBAACwC,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA1CEf,OAAO,CAACT,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Cf,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELjB,gBAAgB,CAAC2B,MAAM,KAAK,CAAC,iBAC5BrD,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvC,OAAA,CAACH,OAAO;QAACyC,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D3C,OAAA;QAAIsC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7E3C,OAAA;QAAGsC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACN,EAGAlC,YAAY,iBACXT,OAAA;MAAKsC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFvC,OAAA;QAAKsC,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDvC,OAAA;UAAIsC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3D3C,OAAA;UAAGsC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9E3C,OAAA;UAAKsC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCvC,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,KAAK,CAAE;YACtC4B,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACvF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3C,OAAA;YAAQsC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAElF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzC,EAAA,CAhNID,QAAQ;AAAAqD,EAAA,GAARrD,QAAQ;AAkNd,eAAeA,QAAQ;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}