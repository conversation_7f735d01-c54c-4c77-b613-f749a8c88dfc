import React, { useState, useEffect } from 'react';
import StatCard from '../components/dashboard/StatCard';
import SalesChart from '../components/dashboard/SalesChart';
import RevenueChart from '../components/dashboard/RevenueChart';
import TrafficSources from '../components/dashboard/TrafficSources';
import { 
  DollarSign, 
  ShoppingCart, 
  Eye, 
  Download, 
  TrendingUp,
  Package,
  Users,
  CreditCard
} from 'lucide-react';
import { ordersAPI, reportsAPI } from '../services/api';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    totalEarnings: 30200,
    totalTasks: 145,
    pageViews: 290,
    downloads: 500,
    salesData: [],
    revenueData: [],
    trafficData: [],
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch order statistics
      const orderStats = await ordersAPI.getStatistics();
      
      // Fetch sales report
      const salesReport = await reportsAPI.sales({
        period: 'week'
      });

      // Update dashboard data with real API data
      setDashboardData(prev => ({
        ...prev,
        totalEarnings: orderStats.data?.total_revenue || prev.totalEarnings,
        totalTasks: orderStats.data?.total_orders || prev.totalTasks,
        pageViews: orderStats.data?.pending_orders || prev.pageViews,
        downloads: orderStats.data?.completed_orders || prev.downloads,
        salesData: salesReport.data?.daily_sales || [],
      }));
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Keep default data if API fails
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening with your store.</p>
        </div>
        <div className="flex space-x-3">
          <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
            Export
          </button>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            Add New
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="All Earnings"
          value={`$${dashboardData.totalEarnings.toLocaleString()}`}
          icon={DollarSign}
          change="10% change on profit"
          changeType="positive"
          color="yellow"
          description="Total revenue this month"
        />
        <StatCard
          title="Task"
          value={dashboardData.totalTasks}
          icon={ShoppingCart}
          change="20% task performance"
          changeType="negative"
          color="red"
          description="Total orders received"
        />
        <StatCard
          title="Page Views"
          value={`${dashboardData.pageViews}+`}
          icon={Eye}
          change="15% daily views"
          changeType="positive"
          color="green"
          description="Website traffic today"
        />
        <StatCard
          title="Downloads"
          value={dashboardData.downloads}
          icon={Download}
          change="% download in App Store"
          changeType="positive"
          color="blue"
          description="App downloads this week"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SalesChart 
          data={dashboardData.salesData}
          title="Sales Per Day"
        />
        <RevenueChart 
          data={dashboardData.revenueData}
          title="Total Revenue"
        />
      </div>

      {/* Bottom Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <TrafficSources data={dashboardData.trafficData} />
        </div>
        
        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <Package className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">New order received</p>
                <p className="text-xs text-gray-500">Order #1234 - $125.00</p>
              </div>
              <span className="text-xs text-gray-400">2m ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">New customer registered</p>
                <p className="text-xs text-gray-500">John Doe joined</p>
              </div>
              <span className="text-xs text-gray-400">5m ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <CreditCard className="w-4 h-4 text-yellow-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Payment processed</p>
                <p className="text-xs text-gray-500">Order #1233 - $89.50</p>
              </div>
              <span className="text-xs text-gray-400">10m ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Sales milestone reached</p>
                <p className="text-xs text-gray-500">$10,000 monthly target</p>
              </div>
              <span className="text-xs text-gray-400">1h ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
