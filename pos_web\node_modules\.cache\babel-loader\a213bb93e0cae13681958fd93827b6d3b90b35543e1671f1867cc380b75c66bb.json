{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\components\\\\form\\\\Alert.js\";\nimport React from \"react\";\nimport { CheckCircle, XCircle, AlertTriangle, Info, X } from \"lucide-react\";\n\n// Mapping styles and icons by status\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst statusStyles = {\n  success: {\n    bg: \"bg-green-50\",\n    text: \"text-green-800\",\n    iconColor: \"text-green-600\",\n    Icon: CheckCircle\n  },\n  error: {\n    bg: \"bg-red-50\",\n    text: \"text-red-800\",\n    iconColor: \"text-red-600\",\n    Icon: XCircle\n  },\n  warning: {\n    bg: \"bg-yellow-50\",\n    text: \"text-yellow-800\",\n    iconColor: \"text-yellow-600\",\n    Icon: AlertTriangle\n  },\n  info: {\n    bg: \"bg-blue-50\",\n    text: \"text-blue-800\",\n    iconColor: \"text-blue-600\",\n    Icon: Info\n  }\n};\nexport const Alert = ({\n  message,\n  onClose,\n  visible,\n  status = \"success\"\n}) => {\n  if (!visible) return null;\n  const {\n    bg,\n    text,\n    iconColor,\n    Icon\n  } = statusStyles[status] || statusStyles.success;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `fixed bottom-0 left-3 items-center justify-between p-4 mb-4 rounded-lg shadow-md ${bg} ${text}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start bottom-0\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          className: `w-5 h-5 mr-2 ${iconColor}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: `${iconColor} hover:opacity-80 transition ml-2`,\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c = Alert;\nvar _c;\n$RefreshReg$(_c, \"Alert\");", "map": {"version": 3, "names": ["React", "CheckCircle", "XCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "X", "jsxDEV", "_jsxDEV", "statusStyles", "success", "bg", "text", "iconColor", "Icon", "error", "warning", "info", "<PERSON><PERSON>", "message", "onClose", "visible", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/components/form/Alert.js"], "sourcesContent": ["import React from \"react\";\r\nimport { <PERSON><PERSON>ircle, XCircle, <PERSON><PERSON><PERSON><PERSON>gle, Info, X } from \"lucide-react\";\r\n\r\n// Mapping styles and icons by status\r\nconst statusStyles = {\r\n  success: {\r\n    bg: \"bg-green-50\",\r\n    text: \"text-green-800\",\r\n    iconColor: \"text-green-600\",\r\n    Icon: CheckCircle,\r\n  },\r\n  error: {\r\n    bg: \"bg-red-50\",\r\n    text: \"text-red-800\",\r\n    iconColor: \"text-red-600\",\r\n    Icon: XCircle,\r\n  },\r\n  warning: {\r\n    bg: \"bg-yellow-50\",\r\n    text: \"text-yellow-800\",\r\n    iconColor: \"text-yellow-600\",\r\n    Icon: AlertTriangle,\r\n  },\r\n  info: {\r\n    bg: \"bg-blue-50\",\r\n    text: \"text-blue-800\",\r\n    iconColor: \"text-blue-600\",\r\n    Icon: Info,\r\n  },\r\n};\r\n\r\nexport const Alert = ({ message, onClose, visible, status = \"success\" }) => {\r\n  if (!visible) return null;\r\n\r\n  const { bg, text, iconColor, Icon } =\r\n    statusStyles[status] || statusStyles.success;\r\n\r\n  return (\r\n    <div\r\n      className={`fixed bottom-0 left-3 items-center justify-between p-4 mb-4 rounded-lg shadow-md ${bg} ${text}`}\r\n    >\r\n      <div className=\"flex items-center justify-between w-full\">\r\n        <div className=\"flex items-start bottom-0\">\r\n          <Icon className={`w-5 h-5 mr-2 ${iconColor}`} />\r\n          <span className=\"text-sm font-medium\">{message}</span>\r\n        </div>\r\n        <button\r\n          onClick={onClose}\r\n          className={`${iconColor} hover:opacity-80 transition ml-2`}\r\n        >\r\n          <X className=\"w-4 h-4\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,OAAO,EAAEC,aAAa,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;;AAE3E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE;IACPC,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAEZ;EACR,CAAC;EACDa,KAAK,EAAE;IACLJ,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,cAAc;IACzBC,IAAI,EAAEX;EACR,CAAC;EACDa,OAAO,EAAE;IACPL,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE,iBAAiB;IAC5BC,IAAI,EAAEV;EACR,CAAC;EACDa,IAAI,EAAE;IACJN,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAE,eAAe;IAC1BC,IAAI,EAAET;EACR;AACF,CAAC;AAED,OAAO,MAAMa,KAAK,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM,GAAG;AAAU,CAAC,KAAK;EAC1E,IAAI,CAACD,OAAO,EAAE,OAAO,IAAI;EAEzB,MAAM;IAAEV,EAAE;IAAEC,IAAI;IAAEC,SAAS;IAAEC;EAAK,CAAC,GACjCL,YAAY,CAACa,MAAM,CAAC,IAAIb,YAAY,CAACC,OAAO;EAE9C,oBACEF,OAAA;IACEe,SAAS,EAAE,oFAAoFZ,EAAE,IAAIC,IAAI,EAAG;IAAAY,QAAA,eAE5GhB,OAAA;MAAKe,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDhB,OAAA;QAAKe,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxChB,OAAA,CAACM,IAAI;UAACS,SAAS,EAAE,gBAAgBV,SAAS;QAAG;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDpB,OAAA;UAAMe,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAEL;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACNpB,OAAA;QACEqB,OAAO,EAAET,OAAQ;QACjBG,SAAS,EAAE,GAAGV,SAAS,mCAAoC;QAAAW,QAAA,eAE3DhB,OAAA,CAACF,CAAC;UAACiB,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAxBWZ,KAAK;AAAA,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}