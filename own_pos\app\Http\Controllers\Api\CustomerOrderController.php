<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductSize;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class CustomerOrderController extends Controller
{
    /**
     * Get customer's order history
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $customerId = $request->get('customer_id');
            
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer ID is required'
                ], 400);
            }

            $query = Order::with(['orderItems.product.sizes', 'orderItems.size', 'deliveryInfo', 'payments'])
                          ->where('customer_id', $customerId);

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter by order type
            if ($request->has('order_type')) {
                $query->where('order_type', $request->get('order_type'));
            }

            // Filter by date range
            if ($request->has('date_from')) {
                $query->whereDate('order_date', '>=', $request->get('date_from'));
            }
            if ($request->has('date_to')) {
                $query->whereDate('order_date', '<=', $request->get('date_to'));
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'order_date');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 10);
            $orders = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders,
                'message' => 'Customer orders retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving customer orders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new customer order
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'customer_id' => 'required|exists:info_customers,customer_id',
                'order_type' => 'required|in:pickup,delivery,dine_in',
                'notes' => 'nullable|string',
                'delivery_address' => 'required_if:order_type,delivery|string',
                'customer_phone' => 'required_if:order_type,delivery|string',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,product_id',
                'items.*.size_id' => 'nullable|exists:product_sizes,size_id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.notes' => 'nullable|string'
            ]);

            DB::beginTransaction();

            // Generate order number
            $orderNumber = 'ORD-' . date('Ymd') . '-' . str_pad(Order::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);

            // Calculate order totals
            $subtotal = 0;
            $orderItems = [];

            foreach ($validated['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);
                
                // Check stock availability
                if ($product->stock_quantity < $item['quantity']) {
                    throw ValidationException::withMessages([
                        'items' => ["Insufficient stock for product: {$product->name_en}. Available: {$product->stock_quantity}"]
                    ]);
                }

                $unitPrice = $product->price;
                
                // Add size price adjustment if size is selected
                if (!empty($item['size_id'])) {
                    $size = ProductSize::findOrFail($item['size_id']);
                    $unitPrice += $size->price_adjustment;
                }

                $totalPrice = $unitPrice * $item['quantity'];
                $subtotal += $totalPrice;

                $orderItems[] = [
                    'product_id' => $item['product_id'],
                    'size_id' => $item['size_id'] ?? null,
                    'quantity' => $item['quantity'],
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'notes' => $item['notes'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }

            $taxAmount = $subtotal * 0.10; // 10% tax
            $deliveryFee = $validated['order_type'] === 'delivery' ? 2.00 : 0.00;
            $totalAmount = $subtotal + $taxAmount + $deliveryFee;

            // Create order
            $order = Order::create([
                'customer_id' => $validated['customer_id'],
                'user_id' => Auth::id() ?? 1, // Default to system user if no auth
                'order_number' => $orderNumber,
                'order_type' => $validated['order_type'],
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'delivery_fee' => $deliveryFee,
                'total_amount' => $totalAmount,
                'notes' => $validated['notes'] ?? null,
                'order_date' => now()
            ]);

            // Create order items and update stock
            foreach ($orderItems as $itemData) {
                $itemData['order_id'] = $order->order_id;
                OrderItem::create($itemData);

                // Update product stock
                $product = Product::find($itemData['product_id']);
                $product->decrement('stock_quantity', $itemData['quantity']);
            }

            // Create delivery info if delivery order
            if ($validated['order_type'] === 'delivery') {
                $order->deliveryInfo()->create([
                    'delivery_address' => $validated['delivery_address'],
                    'customer_phone' => $validated['customer_phone'],
                    'delivery_fee' => $deliveryFee,
                    'delivery_status' => 'pending'
                ]);
            }

            DB::commit();

            $order->load(['customer', 'orderItems.product', 'orderItems.size', 'deliveryInfo']);

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Customer order created successfully'
            ], 201);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating customer order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific customer order details
     */
    public function show(Request $request, $orderId): JsonResponse
    {
        try {
            $customerId = $request->get('customer_id');
            
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer ID is required'
                ], 400);
            }

            $order = Order::with(['customer', 'orderItems.product.sizes', 'orderItems.size', 'deliveryInfo', 'payments'])
                          ->where('order_id', $orderId)
                          ->where('customer_id', $customerId)
                          ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found or does not belong to this customer'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Customer order retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving customer order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel customer order (only if status is pending)
     */
    public function cancel(Request $request, $orderId): JsonResponse
    {
        try {
            $customerId = $request->get('customer_id');
            
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer ID is required'
                ], 400);
            }

            $order = Order::where('order_id', $orderId)
                          ->where('customer_id', $customerId)
                          ->where('status', 'pending')
                          ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found, does not belong to this customer, or cannot be cancelled'
                ], 404);
            }

            DB::beginTransaction();

            // Restore stock quantities
            foreach ($order->orderItems as $item) {
                $product = Product::find($item->product_id);
                $product->increment('stock_quantity', $item->quantity);
            }

            // Update order status
            $order->update(['status' => 'cancelled']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error cancelling order: ' . $e->getMessage()
            ], 500);
        }
    }
}
