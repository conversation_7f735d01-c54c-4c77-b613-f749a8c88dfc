{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\components\\\\ErrorBoundary.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-red-600 mb-4\",\n              children: \"Something went wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: \"An error occurred while loading the application.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n              className: \"text-left bg-gray-100 p-4 rounded-lg mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                className: \"cursor-pointer font-medium text-gray-700\",\n                children: \"Error Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Error:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 38,\n                    columnNumber: 21\n                  }, this), \" \", this.state.error && this.state.error.toString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"mt-2 text-xs overflow-auto\",\n                  children: this.state.errorInfo && this.state.errorInfo.componentStack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n              children: \"Reload Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "setState", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toString", "componentStack", "onClick", "window", "location", "reload"], "sources": ["E:/Developer/Pos_system/pos_web/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from \"react\";\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error: error,\n      errorInfo: errorInfo,\n    });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold text-red-600 mb-4\">\n                Something went wrong\n              </h2>\n              <p className=\"text-gray-600 mb-4\">\n                An error occurred while loading the application.\n              </p>\n              <details className=\"text-left bg-gray-100 p-4 rounded-lg mb-4\">\n                <summary className=\"cursor-pointer font-medium text-gray-700\">\n                  Error Details\n                </summary>\n                <div className=\"mt-2 text-sm text-gray-600\">\n                  <p>\n                    <strong>Error:</strong>{\" \"}\n                    {this.state.error && this.state.error.toString()}\n                  </p>\n                  <pre className=\"mt-2 text-xs overflow-auto\">\n                    {this.state.errorInfo &&\n                      this.state.errorInfo.componentStack}\n                  </pre>\n                </div>\n              </details>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                Reload Page\n              </button>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,SAASH,KAAK,CAACI,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOC,wBAAwBA,CAACF,KAAK,EAAE;IACrC,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAI,iBAAiBA,CAACH,KAAK,EAAEC,SAAS,EAAE;IAClC,IAAI,CAACG,QAAQ,CAAC;MACZJ,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAEAI,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACEN,OAAA;QAAKa,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEd,OAAA;UAAKa,SAAS,EAAC,mDAAmD;UAAAC,QAAA,eAChEd,OAAA;YAAKa,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1Bd,OAAA;cAAIa,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlB,OAAA;cAAGa,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAASa,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBAC5Dd,OAAA;gBAASa,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE9D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACVlB,OAAA;gBAAKa,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCd,OAAA;kBAAAc,QAAA,gBACEd,OAAA;oBAAAc,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAAC,GAAG,EAC1B,IAAI,CAACb,KAAK,CAACE,KAAK,IAAI,IAAI,CAACF,KAAK,CAACE,KAAK,CAACY,QAAQ,CAAC,CAAC;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACJlB,OAAA;kBAAKa,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxC,IAAI,CAACT,KAAK,CAACG,SAAS,IACnB,IAAI,CAACH,KAAK,CAACG,SAAS,CAACY;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACVlB,OAAA;cACEqB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxCX,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACd,KAAK,CAACU,QAAQ;EAC5B;AACF;AAEA,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}