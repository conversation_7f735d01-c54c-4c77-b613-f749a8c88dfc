<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    protected $table = 'payments';
    protected $primaryKey = 'payment_id';

    protected $fillable = [
        'order_id',
        'payment_method',
        'payment_provider',
        'transaction_id',
        'reference_number',
        'qr_code_data',
        'amount',
        'currency',
        'exchange_rate',
        'status',
        'failure_reason',
        'payment_date',
        'confirmed_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'payment_date' => 'datetime',
        'confirmed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the order that owns the payment.
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'order_id');
    }

    /**
     * Get the QR code for the payment.
     */
    public function qrCode()
    {
        return $this->hasOne(PaymentQrCode::class, 'payment_id', 'payment_id');
    }

    /**
     * Get the card details for the payment.
     */
    public function card()
    {
        return $this->hasOne(PaymentCard::class, 'payment_id', 'payment_id');
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
}
