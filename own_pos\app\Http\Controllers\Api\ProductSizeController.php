<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductSize;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ProductSizeController extends Controller
{
    /**
     * Get all sizes for a specific product
     */
    public function index(Request $request, $productId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);
            $sizes = ProductSize::where('product_id', $productId)
                ->orderBy('sort_order')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $sizes,
                'message' => 'Product sizes retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving product sizes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store multiple sizes for a product
     */
    public function store(Request $request, $productId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);

            $validator = Validator::make($request->all(), [
                'sizes' => 'required|array|min:1',
                'sizes.*.size_name_kh' => 'required|string|max:20',
                'sizes.*.size_name_en' => 'required|string|max:20',
                'sizes.*.price_adjustment' => 'required|numeric',
                'sizes.*.is_available' => 'boolean',
                'sizes.*.sort_order' => 'integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $sizes = [];
            foreach ($request->sizes as $index => $sizeData) {
                $sizeData['product_id'] = $productId;
                $sizeData['sort_order'] = $sizeData['sort_order'] ?? $index;
                $sizeData['is_available'] = $sizeData['is_available'] ?? true;
                
                $size = ProductSize::create($sizeData);
                $sizes[] = $size;
            }

            return response()->json([
                'success' => true,
                'data' => $sizes,
                'message' => 'Product sizes created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating product sizes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a specific size
     */
    public function update(Request $request, $productId, $sizeId): JsonResponse
    {
        try {
            $size = ProductSize::where('product_id', $productId)
                ->where('size_id', $sizeId)
                ->firstOrFail();

            $validator = Validator::make($request->all(), [
                'size_name_kh' => 'sometimes|string|max:20',
                'size_name_en' => 'nullable|string|max:20',
                'price_adjustment' => 'sometimes|numeric',
                'is_available' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $size->update($request->only([
                'size_name_kh', 'size_name_en', 'price_adjustment', 
                'is_available', 'sort_order'
            ]));

            return response()->json([
                'success' => true,
                'data' => $size,
                'message' => 'Product size updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating product size: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a specific size
     */
    public function destroy($productId, $sizeId): JsonResponse
    {
        try {
            $size = ProductSize::where('product_id', $productId)
                ->where('size_id', $sizeId)
                ->firstOrFail();

            $size->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product size deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting product size: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update sizes for a product (Replace all sizes)
     */
    public function bulkUpdate(Request $request, $productId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);

            $validator = Validator::make($request->all(), [
                'sizes' => 'required|array',
                'sizes.*.size_id' => 'sometimes|exists:product_sizes,size_id',
                'sizes.*.size_name_kh' => 'required|string|max:20',
                'sizes.*.size_name_en' => 'nullable|string|max:20',
                'sizes.*.price_adjustment' => 'required|numeric',
                'sizes.*.is_available' => 'boolean',
                'sizes.*.sort_order' => 'integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Delete all existing sizes for this product
            ProductSize::where('product_id', $productId)->delete();

            // Create new sizes
            $updatedSizes = [];
            foreach ($request->sizes as $index => $sizeData) {
                $sizeData['product_id'] = $productId;
                $sizeData['sort_order'] = $sizeData['sort_order'] ?? $index;
                $sizeData['is_available'] = $sizeData['is_available'] ?? true;

                // Remove size_id for new creation
                unset($sizeData['size_id']);

                $size = ProductSize::create($sizeData);
                $updatedSizes[] = $size;
            }

            return response()->json([
                'success' => true,
                'data' => $updatedSizes,
                'message' => 'Product sizes updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating product sizes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product with calculated final prices for each size
     */
    public function getProductWithPrices($productId): JsonResponse
    {
        try {
            $product = Product::with(['category', 'sizes' => function($query) {
                $query->orderBy('sort_order');
            }])->findOrFail($productId);

            // Add calculated final prices to sizes
            $product->sizes->each(function($size) use ($product) {
                $size->final_price = $product->price + $size->price_adjustment;
            });

            return response()->json([
                'success' => true,
                'data' => $product,
                'message' => 'Product with prices retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], 404);
        }
    }
}
