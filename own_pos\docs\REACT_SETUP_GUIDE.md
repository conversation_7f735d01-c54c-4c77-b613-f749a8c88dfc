# React.js Frontend Setup Guide

## Quick Start

### 1. Create React App
```bash
npx create-react-app pos-frontend
cd pos-frontend
```

### 2. Install Additional Dependencies
```bash
npm install axios react-router-dom
# Optional: For UI components
npm install @headlessui/react @heroicons/react
# Optional: For styling
npm install tailwindcss
```

### 3. Project Structure
```
src/
├── components/
│   ├── ProductList.jsx
│   ├── ProductForm.jsx
│   ├── ProductSizeManager.jsx
│   └── Layout.jsx
├── hooks/
│   ├── useProducts.js
│   ├── useAuth.js
│   └── useProductSizes.js
├── services/
│   └── api.js
├── contexts/
│   └── AuthContext.js
├── pages/
│   ├── Login.jsx
│   ├── Dashboard.jsx
│   └── Products.jsx
└── App.js
```

### 4. Environment Variables
Create `.env` file in your React app root:
```env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_API_BASE_URL=http://localhost:8000
```

### 5. Basic App.js Setup
```javascript
// src/App.js
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Products from './pages/Products';
import ProtectedRoute from './components/ProtectedRoute';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/products" element={
              <ProtectedRoute>
                <Products />
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
```

### 6. Authentication Context
```javascript
// src/contexts/AuthContext.js
import React, { createContext, useContext, useState, useEffect } from 'react';
import ApiService from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      ApiService.setToken(token);
      // Verify token is still valid
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      const response = await ApiService.request('/user');
      setUser(response.data);
    } catch (error) {
      localStorage.removeItem('auth_token');
      ApiService.setToken(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await ApiService.login(email, password);
      if (response.success) {
        setUser(response.data.user);
        return { success: true };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await ApiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
    }
  };

  const value = {
    user,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 7. Protected Route Component
```javascript
// src/components/ProtectedRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  return user ? children : <Navigate to="/login" />;
};

export default ProtectedRoute;
```

### 8. Products Page Example
```javascript
// src/pages/Products.jsx
import React, { useState } from 'react';
import ProductList from '../components/ProductList';
import ProductForm from '../components/ProductForm';
import Layout from '../components/Layout';

const Products = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);

  const handleEdit = (product) => {
    setEditingProduct(product);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingProduct(null);
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Products</h1>
          <button
            onClick={() => setShowForm(true)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Add Product
          </button>
        </div>

        <ProductList onEdit={handleEdit} />

        {showForm && (
          <ProductForm
            product={editingProduct}
            onClose={handleCloseForm}
          />
        )}
      </div>
    </Layout>
  );
};

export default Products;
```

### 9. Start Development
```bash
# Start Laravel API (in Laravel project directory)
php artisan serve

# Start React app (in React project directory)
npm start
```

Your React app will run on `http://localhost:3000` and connect to the Laravel API on `http://localhost:8000`.

## Key Features to Implement

1. **Authentication Flow**
   - Login/logout
   - Token management
   - Protected routes

2. **Product Management**
   - List products with pagination
   - Create/edit/delete products
   - Search and filter products

3. **Size Management**
   - Add multiple sizes to products
   - Edit size details
   - Calculate final prices

4. **Real-time Updates**
   - Refresh data after operations
   - Error handling
   - Loading states

## API Integration Tips

1. **Always handle errors gracefully**
2. **Use loading states for better UX**
3. **Implement proper token refresh logic**
4. **Cache data when appropriate**
5. **Use React Query or SWR for advanced data fetching**

## Next Steps

1. Implement the components shown in the API documentation
2. Add form validation
3. Implement image upload for products
4. Add real-time notifications
5. Implement offline support
