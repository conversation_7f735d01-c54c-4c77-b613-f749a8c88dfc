import React from "react";
const Haspermission = (requestPemission) => {
  try {
    const userString = sessionStorage.getItem("user");
    if (!userString) return false;

    const useData = JSON.parse(userString);
    const { permissions: userPermissions } = useData;
    const userPermission = (userPermissions || [])
      .map((permission) =>
        typeof permission === "string"
          ? permission.toLowerCase()
          : permission.name?.toLowerCase()
      )
      .filter(Boolean);
    return requestPemission.every((prem) =>
      userPermission.includes(prem.toLowerCase())
    );
  } catch (error) {
    console.error("Error checking permissions:", error);
    return false;
  }
};

export default Haspermission;
