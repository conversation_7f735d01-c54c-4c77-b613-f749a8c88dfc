{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const navigate = useNavigate();\n  const login = () => {\n    setIsAuthenticated(true);\n    navigate(\"/admin/dashboard\");\n  };\n  const logout = () => {\n    setIsAuthenticated(false);\n    sessionStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      isAuthenticated,\n      login,\n      logout\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"fFLPwuop1m+61Uu3ULzyaFpmYmI=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  return useContext(AuthContext);\n};\n_s2(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "isAuthenticated", "setIsAuthenticated", "navigate", "login", "logout", "sessionStorage", "removeItem", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\n\nconst AuthContext = createContext();\n\nexport const AuthProvider = ({ children }) => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const navigate = useNavigate();\n\n  const login = () => {\n    setIsAuthenticated(true);\n    navigate(\"/admin/dashboard\");\n  };\n\n  const logout = () => {\n    setIsAuthenticated(false);\n    sessionStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n\n  return (\n    <AuthContext.Provider value={{ isAuthenticated, login, logout }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => useContext(AuthContext);\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAClE,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,KAAK,GAAGA,CAAA,KAAM;IAClBF,kBAAkB,CAAC,IAAI,CAAC;IACxBC,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,MAAME,MAAM,GAAGA,CAAA,KAAM;IACnBH,kBAAkB,CAAC,KAAK,CAAC;IACzBI,cAAc,CAACC,UAAU,CAAC,MAAM,CAAC;IACjCJ,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEP,OAAA,CAACC,WAAW,CAACW,QAAQ;IAACC,KAAK,EAAE;MAAER,eAAe;MAAEG,KAAK;MAAEC;IAAO,CAAE;IAAAN,QAAA,EAC7DA;EAAQ;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACb,EAAA,CApBWF,YAAY;EAAA,QAENJ,WAAW;AAAA;AAAAoB,EAAA,GAFjBhB,YAAY;AAsBzB,OAAO,MAAMiB,OAAO,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAMxB,UAAU,CAACK,WAAW,CAAC;AAAA;AAACmB,GAAA,CAAxCD,OAAO;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}