<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            
            $products = Product::with(['category', 'sizes' => function($query) {
                $query->orderBy('sort_order');
            }])->get();
            return response()->json([
                'success' => true,
                'data' => $products,
                'message' => 'Products retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = Validator::make($request->all(),[
                'category_id' => 'required|exists:categories,category_id',
                'name_kh' => 'required|string|max:100',
                'name_en' => 'required|string|max:100',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'image_url' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp,ico|max:2048',
                'is_available' => 'boolean',
                'stock_quantity' => 'integer|min:1',
                'min_stock_level' => 'integer|min:1'
            ]);
            $imagePath = null;
            if ($request->hasFile('image_url')) {
                $imagePath = $request->file('image_url')->store('product_images', 'public');
            }
            $product = Product::create([
                'category_id' => $request->category_id,
                'name_kh' => $request->name_kh,
                'name_en' => $request->name_en,
                'description' => $request->description,
                'price' => $request->price,
                'image_url' => $imagePath,
                'is_available' => $request->is_available,
                'stock_quantity' => $request->stock_quantity,
                'min_stock_level' => $request->min_stock_level
            ]);
            return response()->json([
                'success' => true,
                'data' => $product,
                'message' => 'Product created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $product = Product::with(['category', 'sizes'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $product,
                'message' => 'Product retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request,  $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            $validated = $request->validate([
                'category_id' => 'sometimes|exists:categories,category_id',
                'name_kh' => 'sometimes|string|max:100',
                'name_en' => 'nullable|string|max:100',
                'description' => 'nullable|string',
                'price' => 'sometimes|numeric|min:0',
                'image_url' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp,ico|max:2048',
                'is_available' => 'boolean',
                'stock_quantity' => 'integer|min:0',
                'min_stock_level' => 'integer|min:0'
            ]);
            $imagePath = null;
            if ($request->hasFile('image_url')) {
                $imagePath = $request->file('image_url')->store('product_images', 'public');
            }
            $productUpdate = [];
            if(isset($validated['category_id'])) $productUpdate['category_id'] = $validated['category_id'];
            if(isset($validated['name_kh'])) $productUpdate['name_kh'] = $validated['name_kh'];
            if(isset($validated['name_en'])) $productUpdate['name_en'] = $validated['name_en'];
            if(isset($validated['description'])) $productUpdate['description'] = $validated['description'];
            if(isset($validated['price'])) $productUpdate['price'] = $validated['price'];
            if(isset($validated['image_url'])) $productUpdate['image_url'] = $imagePath;
            if(isset($validated['is_available'])) $productUpdate['is_available'] = $validated['is_available'];
            if(isset($validated['stock_quantity'])) $productUpdate['stock_quantity'] = $validated['stock_quantity'];
            if(isset($validated['min_stock_level'])) $productUpdate['min_stock_level'] = $validated['min_stock_level'];
            $product->update($productUpdate);
            return response()->json([
                'success' => true,
                'data' => $product,
                'message' => 'Product updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy( $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);
            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get low stock products
     */
    public function lowStock(): JsonResponse
    {
        try {
            $products = Product::with(['category'])
                ->whereRaw('stock_quantity <= min_stock_level')
                ->orderBy('stock_quantity', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products,
                'message' => 'Low stock products retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving low stock products: ' . $e->getMessage()
            ], 500);
        }
    }
}
