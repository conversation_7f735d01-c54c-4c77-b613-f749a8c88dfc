# POS System API Documentation

## Base URL
```
http://localhost:8000/api
```

## Authentication
This API uses Laravel Sanctum for authentication. Include the bearer token in the Authorization header:

```javascript
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
```

## Authentication Endpoints

### Login
```http
POST /api/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>"
    },
    "token": "1|abc123..."
  },
  "message": "Login successful"
}
```

### Logout
```http
POST /api/logout
```

## Product Management

### Get All Products
```http
GET /api/products
```

**Query Parameters:**
- `search` - Search in product names and description
- `category_id` - Filter by category
- `is_available` - Filter by availability (true/false)
- `low_stock` - Show only low stock products (true/false)
- `sort_by` - Sort field (default: name_kh)
- `sort_order` - Sort direction (asc/desc)
- `per_page` - Items per page (default: 15)

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "product_id": 1,
        "category_id": 1,
        "name_kh": "កាហ្វេ",
        "name_en": "Coffee",
        "description": "Premium coffee",
        "price": "2.50",
        "image_url": null,
        "is_available": true,
        "stock_quantity": 100,
        "min_stock_level": 10,
        "category": {
          "category_id": 1,
          "name_kh": "ភេសជ្ជៈ",
          "name_en": "Beverages"
        },
        "sizes": [
          {
            "size_id": 1,
            "size_name_kh": "តូច",
            "size_name_en": "Small",
            "price_adjustment": "0.00",
            "is_available": true,
            "sort_order": 0
          }
        ]
      }
    ],
    "current_page": 1,
    "total": 1
  }
}
```

### Create Product
```http
POST /api/products
```

**Request Body:**
```json
{
  "category_id": 1,
  "name_kh": "កាហ្វេ",
  "name_en": "Coffee",
  "description": "Premium coffee",
  "price": 2.50,
  "image_url": "https://example.com/image.jpg",
  "is_available": true,
  "stock_quantity": 100,
  "min_stock_level": 10
}
```

### Get Single Product
```http
GET /api/products/{id}
```

### Update Product
```http
PUT /api/products/{id}
```

### Delete Product
```http
DELETE /api/products/{id}
```

### Get Low Stock Products
```http
GET /api/products/low-stock
```

## Product Sizes Management

### Get Product Sizes
```http
GET /api/products/{product_id}/sizes
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "size_id": 1,
      "product_id": 1,
      "size_name_kh": "តូច",
      "size_name_en": "Small",
      "price_adjustment": "0.00",
      "is_available": true,
      "sort_order": 0
    },
    {
      "size_id": 2,
      "product_id": 1,
      "size_name_kh": "មធ្យម",
      "size_name_en": "Medium",
      "price_adjustment": "0.50",
      "is_available": true,
      "sort_order": 1
    }
  ]
}
```

### Create Multiple Sizes
```http
POST /api/products/{product_id}/sizes
```

**Request Body:**
```json
{
  "sizes": [
    {
      "size_name_kh": "តូច",
      "size_name_en": "Small",
      "price_adjustment": 0.00,
      "is_available": true,
      "sort_order": 0
    },
    {
      "size_name_kh": "មធ្យម",
      "size_name_en": "Medium",
      "price_adjustment": 0.50,
      "is_available": true,
      "sort_order": 1
    },
    {
      "size_name_kh": "ធំ",
      "size_name_en": "Large",
      "price_adjustment": 1.00,
      "is_available": true,
      "sort_order": 2
    }
  ]
}
```

### Update Single Size
```http
PUT /api/products/{product_id}/sizes/{size_id}
```

### Delete Size
```http
DELETE /api/products/{product_id}/sizes/{size_id}
```

### Bulk Update All Sizes (Replace All)
```http
PUT /api/products/{product_id}/sizes
```

**Request Body:**
```json
{
  "sizes": [
    {
      "size_name_kh": "តូច",
      "size_name_en": "Small",
      "price_adjustment": 0.00,
      "is_available": true,
      "sort_order": 0
    }
  ]
}
```

### Get Product with Calculated Prices
```http
GET /api/products/{product_id}/with-prices
```

**Response:**
```json
{
  "success": true,
  "data": {
    "product_id": 1,
    "name_kh": "កាហ្វេ",
    "price": "2.50",
    "sizes": [
      {
        "size_id": 1,
        "size_name_kh": "តូច",
        "price_adjustment": "0.00",
        "final_price": 2.50
      },
      {
        "size_id": 2,
        "size_name_kh": "មធ្យម",
        "price_adjustment": "0.50",
        "final_price": 3.00
      }
    ]
  }
}
```

## Categories

### Get All Categories
```http
GET /api/categories
```

### Create Category
```http
POST /api/categories
```

**Request Body:**
```json
{
  "name_kh": "ភេសជ្ជៈ",
  "name_en": "Beverages",
  "description": "All beverages",
  "icon": "coffee",
  "sort_order": 1,
  "is_active": true
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

## HTTP Status Codes

- `200` - Success
- `201` - Created
- `422` - Validation Error
- `404` - Not Found
- `401` - Unauthorized
- `500` - Server Error

## CORS Configuration

The API is configured to accept requests from:
- `http://localhost:3000`
- `http://localhost:3001`
- `http://127.0.0.1:3000`
- `http://127.0.0.1:3001`

Add your React app URL to the CORS configuration in `config/cors.php` if needed.

## React.js Integration Examples

### API Service Class

```javascript
// services/api.js
class ApiService {
  constructor() {
    this.baseURL = 'http://localhost:8000/api';
    this.token = localStorage.getItem('auth_token');
  }

  setToken(token) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(this.token && { 'Authorization': `Bearer ${this.token}` })
    };
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication
  async login(email, password) {
    const response = await this.request('/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });

    if (response.success) {
      this.setToken(response.data.token);
    }

    return response;
  }

  async logout() {
    await this.request('/logout', { method: 'POST' });
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  // Products
  async getProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/products?${queryString}`);
  }

  async createProduct(productData) {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData)
    });
  }

  async updateProduct(id, productData) {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData)
    });
  }

  async deleteProduct(id) {
    return this.request(`/products/${id}`, {
      method: 'DELETE'
    });
  }

  // Product Sizes
  async getProductSizes(productId) {
    return this.request(`/products/${productId}/sizes`);
  }

  async createProductSizes(productId, sizes) {
    return this.request(`/products/${productId}/sizes`, {
      method: 'POST',
      body: JSON.stringify({ sizes })
    });
  }

  async updateProductSizes(productId, sizes) {
    return this.request(`/products/${productId}/sizes`, {
      method: 'PUT',
      body: JSON.stringify({ sizes })
    });
  }

  async getProductWithPrices(productId) {
    return this.request(`/products/${productId}/with-prices`);
  }
}

export default new ApiService();
```

### React Hook for Products

```javascript
// hooks/useProducts.js
import { useState, useEffect } from 'react';
import ApiService from '../services/api';

export const useProducts = (params = {}) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getProducts(params);
      setProducts(response.data.data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [JSON.stringify(params)]);

  const createProduct = async (productData) => {
    try {
      const response = await ApiService.createProduct(productData);
      await fetchProducts(); // Refresh list
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const updateProduct = async (id, productData) => {
    try {
      const response = await ApiService.updateProduct(id, productData);
      await fetchProducts(); // Refresh list
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteProduct = async (id) => {
    try {
      await ApiService.deleteProduct(id);
      await fetchProducts(); // Refresh list
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return {
    products,
    loading,
    error,
    createProduct,
    updateProduct,
    deleteProduct,
    refetch: fetchProducts
  };
};
```

### Product Component Example

```javascript
// components/ProductList.jsx
import React from 'react';
import { useProducts } from '../hooks/useProducts';

const ProductList = () => {
  const { products, loading, error, deleteProduct } = useProducts();

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {products.map(product => (
        <div key={product.product_id} className="border rounded-lg p-4">
          <h3 className="font-bold">{product.name_en || product.name_kh}</h3>
          <p className="text-gray-600">{product.description}</p>
          <p className="text-lg font-semibold">${product.price}</p>

          {product.sizes && product.sizes.length > 0 && (
            <div className="mt-2">
              <h4 className="font-medium">Available Sizes:</h4>
              <div className="flex flex-wrap gap-1">
                {product.sizes.map(size => (
                  <span
                    key={size.size_id}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                  >
                    {size.size_name_en || size.size_name_kh}
                    {size.price_adjustment > 0 && ` (+$${size.price_adjustment})`}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div className="mt-4 flex gap-2">
            <button
              onClick={() => {/* Handle edit */}}
              className="bg-blue-500 text-white px-3 py-1 rounded"
            >
              Edit
            </button>
            <button
              onClick={() => deleteProduct(product.product_id)}
              className="bg-red-500 text-white px-3 py-1 rounded"
            >
              Delete
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductList;
```
