import React from 'react';
import {
  Chart as ChartJ<PERSON>,
  ArcElement,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'chart.js';
import { Doughn<PERSON> } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);

const RevenueChart = ({ data = [], title = "Total Revenue" }) => {
  const defaultData = [
    { source: 'Youtube', value: 16.8, color: '#EF4444' },
    { source: 'Facebook', value: 45.3, color: '#3B82F6' },
    { source: 'Twitter', value: 37.9, color: '#06B6D4' },
  ];

  const chartData = data.length > 0 ? data : defaultData;

  const doughnutData = {
    labels: chartData.map(item => item.source),
    datasets: [
      {
        data: chartData.map(item => item.value),
        backgroundColor: chartData.map(item => item.color),
        borderWidth: 0,
        cutout: '70%',
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(59, 130, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            return `${context.label}: ${context.parsed}%`;
          }
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">{title}</h3>
      
      <div className="flex items-center justify-between">
        <div className="w-40 h-40">
          <Doughnut data={doughnutData} options={options} />
        </div>
        
        <div className="flex-1 ml-6">
          <div className="space-y-4">
            {chartData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-3 h-3 rounded-full mr-3"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm text-gray-600">{item.source}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {item.value}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {item.source === 'Youtube' && '+16.8%'}
                    {item.source === 'Facebook' && '+45.3%'}
                    {item.source === 'Twitter' && '+37.9%'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RevenueChart;
