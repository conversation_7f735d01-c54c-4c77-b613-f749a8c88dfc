{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polyline\", {\n  points: \"3.5 2 6.5 12.5 18 12.5\",\n  key: \"y3iy52\"\n}], [\"line\", {\n  x1: \"9.5\",\n  x2: \"5.5\",\n  y1: \"12.5\",\n  y2: \"20\",\n  key: \"19vg5i\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"18.5\",\n  y1: \"12.5\",\n  y2: \"20\",\n  key: \"1inpmv\"\n}], [\"path\", {\n  d: \"M2.75 18a13 13 0 0 0 18.5 0\",\n  key: \"1nquas\"\n}]];\nconst Rocking<PERSON>hair = createLucideIcon(\"rocking-chair\", __iconNode);\nexport { __iconNode, Rocking<PERSON>hair as default };", "map": {"version": 3, "names": ["__iconNode", "points", "key", "x1", "x2", "y1", "y2", "d", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["E:\\Developer\\Pos_system\\pos_web\\node_modules\\lucide-react\\src\\icons\\rocking-chair.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['polyline', { points: '3.5 2 6.5 12.5 18 12.5', key: 'y3iy52' }],\n  ['line', { x1: '9.5', x2: '5.5', y1: '12.5', y2: '20', key: '19vg5i' }],\n  ['line', { x1: '15', x2: '18.5', y1: '12.5', y2: '20', key: '1inpmv' }],\n  ['path', { d: 'M2.75 18a13 13 0 0 0 18.5 0', key: '1nquas' }],\n];\n\n/**\n * @component @name RockingChair\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIzLjUgMiA2LjUgMTIuNSAxOCAxMi41IiAvPgogIDxsaW5lIHgxPSI5LjUiIHgyPSI1LjUiIHkxPSIxMi41IiB5Mj0iMjAiIC8+CiAgPGxpbmUgeDE9IjE1IiB4Mj0iMTguNSIgeTE9IjEyLjUiIHkyPSIyMCIgLz4KICA8cGF0aCBkPSJNMi43NSAxOGExMyAxMyAwIDAgMCAxOC41IDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rocking-chair\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RockingChair = createLucideIcon('rocking-chair', __iconNode);\n\nexport default RockingChair;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,UAAY;EAAEC,MAAA,EAAQ,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAEK,CAAA,EAAG,6BAA+B;EAAAL,GAAA,EAAK;AAAU,GAC9D;AAaM,MAAAM,YAAA,GAAeC,gBAAiB,kBAAiBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}