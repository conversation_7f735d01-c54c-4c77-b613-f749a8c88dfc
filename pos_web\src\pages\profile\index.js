import React, { useState, useEffect, useRef } from "react";
import { User, Edit3, Save, X, Camera, Upload } from "lucide-react";
import { authAPI, usersAPI } from "../../services/api";

const Profile = () => {
  const [userData, setUserData] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    first_name_kh: "",
    last_name_kh: "",
    gender: "",
    date_of_birth: "",
    phone: "",
    current_address: "",
    photo: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [profilePhoto, setProfilePhoto] = useState(null);
  const [photoPreview, setPhotoPreview] = useState(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      const userString = sessionStorage.getItem("user");
      if (userString) {
        const storedUserData = JSON.parse(userString);
        const userId = storedUserData.user.id;
        const response = await authAPI.me(userId);
        setUserData(response.data);
        setFormData({
          name: response.data.name || "",
          email: response.data.email || "",
          first_name_kh: response.data.info?.first_name_kh || "",
          last_name_kh: response.data.info?.last_name_kh || "",
          gender: response.data.info?.gender || "",
          date_of_birth: response.data.info?.date_of_birth || "",
          phone: response.data.info?.phone || "",
          current_address: response.data.info?.current_address || "",
          photo: response.data.info?.photo || "",
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      if (!validTypes.includes(file.type)) {
        setErrors((prev) => ({
          ...prev,
          photo: "Please select a valid image file (JPEG, PNG, GIF)",
        }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          photo: "Image size must be less than 5MB",
        }));
        return;
      }

      // Clear any previous photo errors
      setErrors((prev) => ({
        ...prev,
        photo: "",
      }));

      setProfilePhoto(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePhotoRemove = () => {
    setProfilePhoto(null);
    setPhotoPreview(null);
    setFormData((prev) => ({
      ...prev,
      photo: "",
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    if (!formData.first_name_kh.trim()) {
      newErrors.first_name_kh = "First name is required";
    }
    if (!formData.last_name_kh.trim()) {
      newErrors.last_name_kh = "Last name is required";
    }
    if (!formData.gender.trim()) {
      newErrors.gender = "Gender is required";
    }
    if (!formData.date_of_birth.trim()) {
      newErrors.date_of_birth = "Date of birth is required";
    }
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone is required";
    }
    if (!formData.current_address.trim()) {
      newErrors.current_address = "Current address is required";
    }

    if (formData.newPassword) {
      if (!formData.currentPassword) {
        newErrors.currentPassword =
          "Current password is required to change password";
      }
      if (formData.newPassword.length < 6) {
        newErrors.newPassword = "New password must be at least 6 characters";
      }
      if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    setSaving(true);
    try {
      // Create FormData for file upload
      const formDataToSend = new FormData();

      // Add basic fields
      formDataToSend.append("name", formData.name);
      formDataToSend.append("email", formData.email);

      // Only add password if it's being changed
      if (formData.newPassword) {
        formDataToSend.append("password", formData.newPassword);
        formDataToSend.append("current_password", formData.currentPassword);
      }

      // Add additional info fields
      formDataToSend.append("first_name_kh", formData.first_name_kh || "");
      formDataToSend.append("last_name_kh", formData.last_name_kh || "");
      formDataToSend.append("gender", formData.gender || "");
      formDataToSend.append("date_of_birth", formData.date_of_birth || "");
      formDataToSend.append("phone", formData.phone || "");
      formDataToSend.append("current_address", formData.current_address || "");

      // Add photo if a new one was selected
      if (profilePhoto) {
        formDataToSend.append("photo", profilePhoto);
      }

      // Add method override for PUT request (Laravel requirement)
      formDataToSend.append("_method", "PUT");

      console.log("Sending data:", Object.fromEntries(formDataToSend));
      const response = await usersAPI.update(userData.id, formDataToSend);
      console.log("Update response:", response);
      setTimeout(() => {
        setIsEditing(false);
        setSaving(false);

        setUserData((prev) => ({
          ...prev,
          name: formData.name,
          email: formData.email,
        }));
        // Clear password fields
        setFormData((prev) => ({
          ...prev,
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        }));
      }, 1000);
    } catch (error) {
      console.error("Error updating profile:", error);
      setSaving(false);

      // Handle validation errors from server
      if (error.response?.status === 422 && error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        // Show general error message
        setErrors({
          general:
            error.response?.data?.message ||
            "Failed to update profile. Please try again.",
        });
      }
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setErrors({});
    // Reset photo state
    setProfilePhoto(null);
    setPhotoPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    // Reset form data
    setFormData({
      name: userData?.name || "",
      email: userData?.email || "",
      first_name_kh: userData?.info?.first_name_kh || "",
      last_name_kh: userData?.info?.last_name_kh || "",
      gender: userData?.info?.gender || "",
      date_of_birth: userData?.info?.date_of_birth || "",
      phone: userData?.info?.phone || "",
      current_address: userData?.info?.current_address || "",
      photo: userData?.info?.photo || "",
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className=" min-h-screen bg-gray-5o">
      <div className=" mx-auto  ">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="md:flex">
            {/* Left Side - Profile Info */}
            <div className="md:w-1/3 bg-gradient-to-b from-blue-600 to-blue-700 p-8 text-white text-center">
              <div className="mb-6">
                <div className="relative w-32 h-32 mx-auto mb-4">
                  <div className="w-32 h-32 bg-white rounded-full flex items-center justify-center overflow-hidden">
                    {photoPreview ? (
                      <img
                        src={photoPreview}
                        alt="Profile Preview"
                        className="w-full h-full object-cover rounded-full"
                      />
                    ) : userData?.info?.photo ? (
                      <img
                        src={`http://localhost:8000/storage/${userData?.info?.photo}`}
                        alt="User"
                        className="w-full h-full object-cover rounded-full"
                      />
                    ) : (
                      <User className="w-16 h-16 text-blue-600" />
                    )}
                  </div>

                  {isEditing && (
                    <div className="absolute bottom-0 right-0">
                      <button
                        onClick={triggerFileInput}
                        className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors"
                        title="Change Photo"
                      >
                        <Camera className="w-4 h-4" />
                      </button>
                    </div>
                  )}
                </div>

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoChange}
                  className="hidden"
                />

                {/* Photo upload section for editing mode */}
                {isEditing && (
                  <div className="mb-4">
                    <div className="flex flex-col space-y-2">
                      <button
                        onClick={triggerFileInput}
                        className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center justify-center"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Photo
                      </button>

                      {(photoPreview || userData?.info?.photo) && (
                        <button
                          onClick={handlePhotoRemove}
                          className="bg-red-500/20 hover:bg-red-500/30 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center justify-center"
                        >
                          <X className="w-4 h-4 mr-2" />
                          Remove Photo
                        </button>
                      )}
                    </div>

                    {errors.photo && (
                      <p className="text-red-200 text-xs mt-2">
                        {errors.photo}
                      </p>
                    )}
                  </div>
                )}
                <h2 className="text-2xl font-bold mb-2">{userData?.name}</h2>
                <p className="text-blue-100 mb-4">
                  {userData?.roles?.[0] || "User"}
                </p>

                {!isEditing && (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors flex items-center mx-auto"
                  >
                    <Edit3 className="w-4 h-4 mr-2" />
                    Edit Profile
                  </button>
                )}
              </div>

              <div className="text-left space-y-4">
                <div>
                  <p className="text-blue-200 text-sm">Email</p>
                  <p className="font-medium">{userData?.email}</p>
                </div>
                <div>
                  <p className="text-blue-200 text-sm">Member Since</p>
                  <p className="font-medium">
                    {userData?.created_at
                      ? new Date(userData.created_at).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-blue-200 text-sm">Permissions</p>
                  <p className="font-medium">
                    {userData?.permissions?.length || 0} permissions
                  </p>
                </div>
              </div>
            </div>

            {/* Right Side - Edit Form */}
            <div className="md:w-2/3 p-8">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-gray-900">
                  {isEditing ? "Edit Profile" : "Profile Information"}
                </h3>
                {isEditing && (
                  <div className="flex space-x-3">
                    <button
                      onClick={handleCancel}
                      className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
                    >
                      {saving ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      ) : (
                        <Save className="w-4 h-4 mr-2" />
                      )}
                      {saving ? "Saving..." : "Save Changes"}
                    </button>
                  </div>
                )}
              </div>

              {/* General Error Display */}
              {errors.general && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{errors.general}</p>
                </div>
              )}

              <div className="space-y-6">
                {/* Basic Information */}
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        First Name (Khmer)
                      </label>
                      {isEditing ? (
                        <div>
                          <input
                            type="text"
                            name="first_name_kh"
                            value={formData.first_name_kh}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter your first name in Khmer"
                          />
                          {errors.first_name_kh && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.first_name_kh}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="text"
                          name="first_name_kh"
                          value={userData?.info?.first_name_kh}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Last Name (Khmer)
                      </label>
                      {isEditing ? (
                        <div>
                          <input
                            type="text"
                            name="last_name_kh"
                            value={formData.last_name_kh}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter your first name in Khmer"
                          />
                          {errors.last_name_kh && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.last_name_kh}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="text"
                          name="last_name_kh"
                          value={userData?.info?.last_name_kh}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Full Name (English)
                      </label>
                      {isEditing ? (
                        <div>
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter your full name"
                          />
                          {errors.name && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.name}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="text"
                          name="name"
                          value={userData?.name}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Gender
                      </label>
                      {isEditing ? (
                        <div>
                          <select
                            name="gender"
                            value={formData.gender}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                          </select>

                          {errors.gender && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.gender}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="text"
                          name="gender"
                          value={userData?.info?.gender}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Date of Birth
                      </label>
                      {isEditing ? (
                        <div>
                          <input
                            type="date"
                            name="date_of_birth"
                            value={formData.date_of_birth}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          {errors.date_of_birth && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.date_of_birth}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="date"
                          name="date_of_birth"
                          value={userData?.info?.date_of_birth}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      {isEditing ? (
                        <div>
                          <input
                            type="text"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          {errors.phone && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.phone}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="text"
                          name="phone"
                          value={userData?.info?.phone}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Current Address
                      </label>
                      {isEditing ? (
                        <div>
                          <input
                            type="text"
                            name="current_address"
                            value={formData.current_address}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          {errors.current_address && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.current_address}
                            </p>
                          )}
                        </div>
                      ) : (
                        <input
                          type="text"
                          name="current_address"
                          value={userData?.info?.current_address}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Password Section - Only show when editing */}
                {isEditing && (
                  <div>
                    <div>
                      <label className="flex text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>

                      <div>
                        <input
                          type="text"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.email}
                          </p>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        Change Password
                      </h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Current Password
                          </label>
                          <input
                            type="password"
                            name="currentPassword"
                            value={formData.currentPassword}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter current password"
                          />
                          {errors.currentPassword && (
                            <p className="mt-1 text-sm text-red-600">
                              {errors.currentPassword}
                            </p>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              New Password
                            </label>
                            <input
                              type="password"
                              name="newPassword"
                              value={formData.newPassword}
                              onChange={handleInputChange}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter new password"
                            />
                            {errors.newPassword && (
                              <p className="mt-1 text-sm text-red-600">
                                {errors.newPassword}
                              </p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Confirm New Password
                            </label>
                            <input
                              type="password"
                              name="confirmPassword"
                              value={formData.confirmPassword}
                              onChange={handleInputChange}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Confirm new password"
                            />
                            {errors.confirmPassword && (
                              <p className="mt-1 text-sm text-red-600">
                                {errors.confirmPassword}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
