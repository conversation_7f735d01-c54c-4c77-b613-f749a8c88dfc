<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StockAlertService;
use App\Services\TelegramService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    private $stockAlertService;
    private $telegramService;

    public function __construct(StockAlertService $stockAlertService, TelegramService $telegramService)
    {
        $this->stockAlertService = $stockAlertService;
        $this->telegramService = $telegramService;
    }

    /**
     * Check and send low stock alerts
     */
    public function checkStockAlerts(): JsonResponse
    {
        try {
            $result = $this->stockAlertService->checkAndSendLowStockAlerts();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => [
                    'products_count' => $result['products_count'],
                    'products' => $result['products'] ?? []
                ]
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error checking stock alerts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Force send stock alerts (bypass daily cache)
     */
    public function forceStockAlert(): JsonResponse
    {
        try {
            $result = $this->stockAlertService->forceSendStockAlert();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => [
                    'products_count' => $result['products_count'],
                    'products' => $result['products'] ?? []
                ]
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending stock alert: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get stock statistics
     */
    public function getStockStatistics(): JsonResponse
    {
        try {
            $stats = $this->stockAlertService->getStockStatistics();

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Stock statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving stock statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send weekly stock report
     */
    public function sendWeeklyStockReport(): JsonResponse
    {
        try {
            $sent = $this->stockAlertService->sendWeeklyStockReport();

            return response()->json([
                'success' => $sent,
                'message' => $sent ? 'Weekly stock report sent successfully' : 'Failed to send weekly stock report'
            ], $sent ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending weekly stock report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send custom notification
     */
    public function sendCustomNotification(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'message' => 'required|string|max:4000',
                'chat_id' => 'nullable|string'
            ]);

            $options = [];
            if (!empty($validated['chat_id'])) {
                $options['chat_id'] = $validated['chat_id'];
            }

            $sent = $this->telegramService->sendMessage($validated['message'], $options);

            return response()->json([
                'success' => $sent,
                'message' => $sent ? 'Notification sent successfully' : 'Failed to send notification'
            ], $sent ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send order notification
     */
    public function sendOrderNotification(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'order_data' => 'required|array',
                'type' => 'required|in:new,completed,cancelled,preparing,ready'
            ]);

            $sent = $this->telegramService->sendOrderNotification(
                $validated['order_data'],
                $validated['type']
            );

            return response()->json([
                'success' => $sent,
                'message' => $sent ? 'Order notification sent successfully' : 'Failed to send order notification'
            ], $sent ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending order notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send delivery notification
     */
    public function sendDeliveryNotification(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'delivery_data' => 'required|array',
                'status' => 'required|in:assigned,picked_up,in_transit,delivered,failed'
            ]);

            $sent = $this->telegramService->sendDeliveryNotification(
                $validated['delivery_data'],
                $validated['status']
            );

            return response()->json([
                'success' => $sent,
                'message' => $sent ? 'Delivery notification sent successfully' : 'Failed to send delivery notification'
            ], $sent ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending delivery notification: ' . $e->getMessage()
            ], 500);
        }
    }
}
