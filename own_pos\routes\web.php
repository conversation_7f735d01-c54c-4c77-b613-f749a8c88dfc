<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;

Route::get('/', function () {
    return response()->json([
        'message' => 'POS System API',
        'version' => '1.0.0',
        'documentation' => '/api/docs',
        'endpoints' => [
            'auth' => '/api/login',
            'products' => '/api/products',
            'categories' => '/api/categories',
            'customers' => '/api/customers',
            'orders' => '/api/orders'
        ]
    ]);
});

// Authentication Routes (Guest only)
Route::middleware('guest')->group(function () {
    Route::get('/login', function (Illuminate\Http\Request $request) {
        if ($request->has('direct')) {
            return app(App\Http\Controllers\AuthController::class)->showLoginForm();
        }
        return view('auth.check-session-storage', ['originalUrl' => '/login']);
    })->name('login');

    Route::post('/login', [AuthController::class, 'login']);

    Route::get('/register', function (Illuminate\Http\Request $request) {
        if ($request->has('direct')) {
            return app(App\Http\Controllers\AuthController::class)->showRegistrationForm();
        }
        return view('auth.check-session-storage', ['originalUrl' => '/register']);
    })->name('register');

    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected Routes (for web-based admin if needed)
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard');
});

// Authentication test page (public for testing)
Route::get('/auth-test', function () {
    return view('auth-test');
})->name('auth-test');
