import React, { useState, useEffect } from 'react';
import { productsAPI } from '../services/api';
import { AlertTriangle, Package, Edit, Plus } from 'lucide-react';
import { Link } from 'react-router-dom';

const LowStockProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLowStockProducts();
  }, []);

  const fetchLowStockProducts = async () => {
    try {
      setLoading(true);
      const response = await productsAPI.getAll();
      // Filter products with stock less than 10
      const lowStockProducts = (response.data.data || []).filter(product => 
        (product.stock || 0) < 10 && (product.stock || 0) > 0
      );
      setProducts(lowStockProducts);
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      // Use mock low stock data if API fails
      setProducts([
        {
          id: 2,
          name: 'Organic Tea Blend',
          category: 'Beverages',
          price: 18.50,
          stock: 5,
          status: 'active',
          image: '/api/placeholder/100/100'
        },
        {
          id: 4,
          name: 'Special Blend Coffee',
          category: 'Beverages',
          price: 22.99,
          stock: 3,
          status: 'active',
          image: '/api/placeholder/100/100'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getStockStatus = (stock) => {
    const stockValue = stock || 0;
    if (stockValue === 0) return { text: 'Out of Stock', color: 'text-red-600 bg-red-100' };
    if (stockValue < 10) return { text: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' };
    return { text: 'In Stock', color: 'text-green-600 bg-green-100' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <AlertTriangle className="w-6 h-6 text-yellow-500 mr-2" />
            Low Stock Products
          </h1>
          <p className="text-gray-600">Products that need restocking (less than 10 items)</p>
        </div>
        <Link
          to="/admin/products"
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Package className="w-4 h-4 mr-2" />
          View All Products
        </Link>
      </div>

      {/* Alert Banner */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800">
              {products.length} product{products.length !== 1 ? 's' : ''} running low on stock
            </h3>
            <p className="text-sm text-yellow-700 mt-1">
              Consider restocking these items to avoid stockouts.
            </p>
          </div>
        </div>
      </div>

      {/* Products List */}
      {products.length > 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((product) => {
                  const stockStatus = getStockStatus(product.stock);
                  return (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
                              <Package className="w-5 h-5 text-gray-400" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {product.name || 'Unnamed Product'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product.category || 'Uncategorized'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${product.price || '0.00'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900 mr-2">
                            {product.stock || 0}
                          </span>
                          <AlertTriangle className="w-4 h-4 text-yellow-500" />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>
                          {stockStatus.text}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900 mr-3">
                          <Edit className="w-4 h-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-900">
                          <Plus className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No low stock products</h3>
          <p className="text-gray-600">All products are well stocked!</p>
        </div>
      )}
    </div>
  );
};

export default LowStockProducts;
