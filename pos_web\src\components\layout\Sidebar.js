import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  FileText,
  Settings,
  BarChart3,
  Truck,
  CreditCard,
  Bell,
  Shield,
  Tag,
} from 'lucide-react';

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();

  const menuItems = [
    {
      title: "Dashboard",
      icon: LayoutDashboard,
      path: "",
    },
    {
      title: "Products",
      icon: Package,
      path: "/admin/products",
      submenu: [
        { title: "All Products", path: "/admin/products/all" },
        { title: "Categories", path: "/admin/categories" },
        { title: "Low Stock", path: "/admin/products/low-stock" },
      ],
    },
    {
      title: "Orders",
      icon: ShoppingCart,
      path: "/admin/orders",
      submenu: [
        { title: "All Orders", path: "/admin/orders" },
        { title: "Customer Orders", path: "/admin/customer-orders" },
        { title: "Order Statistics", path: "/admin/orders/statistics" },
      ],
    },
    {
      title: "Customers",
      icon: Users,
      path: "/admin/customers",
    },
    {
      title: "Payments",
      icon: CreditCard,
      path: "/admin/payments",
    },
    {
      title: "Deliveries",
      icon: Truck,
      path: "/admin/deliveries",
    },
    {
      title: "Reports",
      icon: BarChart3,
      path: "/admin/reports",
      submenu: [
        { title: "Sales Report", path: "/admin/reports/sales" },
        { title: "Inventory Report", path: "/admin/reports/inventory" },
        { title: "Buy-In Sell-Out", path: "/admin/reports/buy-sell" },
        { title: "Customer Feedback", path: "/admin/reports/feedback" },
        { title: "Delivery Performance", path: "/admin/reports/delivery" },
      ],
    },
    {
      title: "Notifications",
      icon: Bell,
      path: "/admin/notifications",
    },
    {
      title: "User Management",
      icon: Shield,
      path: "/admin/users",
      submenu: [
        { title: "Users", path: "/admin/users" },
        { title: "Roles & Permissions", path: "/admin/permissions" },
      ],
    },
    {
      title: "Settings",
      icon: Settings,
      path: "/admin/settings",
    },
  ];

  const isActiveLink = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex items-center justify-center h-16 px-4 bg-blue-600">
          <h1 className="text-xl font-bold text-white">POS Admin</h1>
        </div>

        <nav className="mt-8 px-4">
          <div className="space-y-2">
            {menuItems.map((item) => (
              <div key={item.path}>
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive || isActiveLink(item.path)
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                  onClick={() => {
                    if (window.innerWidth < 1024) {
                      onClose();
                    }
                  }}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.title}
                </NavLink>

                {/* Submenu */}
                {item.submenu && isActiveLink(item.path) && (
                  <div className="ml-8 mt-2 space-y-1">
                    {item.submenu.map((subItem) => (
                      <NavLink
                        key={subItem.path}
                        to={subItem.path}
                        className={({ isActive }) =>
                          `block px-4 py-2 text-sm rounded-lg transition-colors duration-200 ${
                            isActive
                              ? 'bg-blue-50 text-blue-600'
                              : 'text-gray-600 hover:bg-gray-50'
                          }`
                        }
                        onClick={() => {
                          if (window.innerWidth < 1024) {
                            onClose();
                          }
                        }}
                      >
                        {subItem.title}
                      </NavLink>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </nav>
      </div>
    </>
  );
};

export default Sidebar;
