<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryInfo extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'delivery_info';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'delivery_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'order_id',
        'driver_id',
        'delivery_address',
        'customer_phone',
        'delivery_fee',
        'delivery_status',
        'estimated_time',
        'pickup_time',
        'delivery_time',
        'tracking_notes'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'delivery_fee' => 'decimal:2',
        'estimated_time' => 'datetime',
        'pickup_time' => 'datetime',
        'delivery_time' => 'datetime'
    ];

    /**
     * Get the order that owns the delivery.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'order_id');
    }

    /**
     * Get the driver assigned to the delivery.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(DeliveryDriver::class, 'driver_id', 'driver_id');
    }

    /**
     * Get the tracking records for the delivery.
     */
    public function trackingRecords(): HasMany
    {
        return $this->hasMany(DeliveryTracking::class, 'delivery_id', 'delivery_id');
    }

    /**
     * Get the notifications for the delivery.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(DeliveryNotification::class, 'delivery_id', 'delivery_id');
    }

    /**
     * Scope a query to only include deliveries with a specific status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('delivery_status', $status);
    }

    /**
     * Scope a query to only include deliveries for a specific driver.
     */
    public function scopeForDriver($query, $driverId)
    {
        return $query->where('driver_id', $driverId);
    }

    /**
     * Check if the delivery is assigned.
     */
    public function isAssigned(): bool
    {
        return $this->delivery_status === 'assigned';
    }

    /**
     * Check if the delivery is picked up.
     */
    public function isPickedUp(): bool
    {
        return $this->delivery_status === 'picked_up';
    }

    /**
     * Check if the delivery is on the way.
     */
    public function isOnTheWay(): bool
    {
        return $this->delivery_status === 'on_the_way';
    }

    /**
     * Check if the delivery is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->delivery_status === 'delivered';
    }

    /**
     * Check if the delivery is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->delivery_status === 'cancelled';
    }
}
