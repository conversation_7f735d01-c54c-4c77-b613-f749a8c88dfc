{"ast": null, "code": "var _jsxFileName = \"E:\\\\Developer\\\\Pos_system\\\\pos_web\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { LayoutDashboard, Package, ShoppingCart, Users, FileText, Settings, BarChart3, Truck, CreditCard, Bell, Shield, Tag } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isOpen,\n  onClose\n}) => {\n  _s();\n  const location = useLocation();\n  const menuItems = [{\n    title: 'Dashboard',\n    icon: LayoutDashboard,\n    path: '/admin/dashboard'\n  }, {\n    title: 'Products',\n    icon: Package,\n    path: '/admin/pducts',\n    submenu: [{\n      title: 'All Products',\n      path: '/admin/products'\n    }, {\n      title: 'Categories',\n      path: '/admin/categories'\n    }, {\n      title: 'Low Stock',\n      path: '/admin/products/low-stock'\n    }]\n  }, {\n    title: 'Orders',\n    icon: ShoppingCart,\n    path: '/admin/orders',\n    submenu: [{\n      title: 'All Orders',\n      path: '/admin/orders'\n    }, {\n      title: 'Customer Orders',\n      path: '/admin/customer-orders'\n    }, {\n      title: 'Order Statistics',\n      path: '/admin/orders/statistics'\n    }]\n  }, {\n    title: 'Customers',\n    icon: Users,\n    path: '/admin/customers'\n  }, {\n    title: 'Payments',\n    icon: CreditCard,\n    path: '/admin/payments'\n  }, {\n    title: 'Deliveries',\n    icon: Truck,\n    path: '/admin/deliveries'\n  }, {\n    title: 'Reports',\n    icon: BarChart3,\n    path: '/admin/reports',\n    submenu: [{\n      title: 'Sales Report',\n      path: '/admin/reports/sales'\n    }, {\n      title: 'Inventory Report',\n      path: '/admin/reports/inventory'\n    }, {\n      title: 'Buy-In Sell-Out',\n      path: '/admin/reports/buy-sell'\n    }, {\n      title: 'Customer Feedback',\n      path: '/admin/reports/feedback'\n    }, {\n      title: 'Delivery Performance',\n      path: '/admin/reports/delivery'\n    }]\n  }, {\n    title: 'Notifications',\n    icon: Bell,\n    path: '/admin/notifications'\n  }, {\n    title: 'User Management',\n    icon: Shield,\n    path: '/admin/users',\n    submenu: [{\n      title: 'Users',\n      path: '/admin/users'\n    }, {\n      title: 'Roles & Permissions',\n      path: '/admin/permissions'\n    }]\n  }, {\n    title: 'Settings',\n    icon: Settings,\n    path: '/admin/settings'\n  }];\n  const isActiveLink = path => {\n    return location.pathname === path || location.pathname.startsWith(path + '/');\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${isOpen ? 'translate-x-0' : '-translate-x-full'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-16 px-4 bg-blue-600\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"POS Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-8 px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(NavLink, {\n              to: item.path,\n              className: ({\n                isActive\n              }) => `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${isActive || isActiveLink(item.path) ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`,\n              onClick: () => {\n                if (window.innerWidth < 1024) {\n                  onClose();\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                className: \"mr-3 h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), item.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), item.submenu && isActiveLink(item.path) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-8 mt-2 space-y-1\",\n              children: item.submenu.map(subItem => /*#__PURE__*/_jsxDEV(NavLink, {\n                to: subItem.path,\n                className: ({\n                  isActive\n                }) => `block px-4 py-2 text-sm rounded-lg transition-colors duration-200 ${isActive ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`,\n                onClick: () => {\n                  if (window.innerWidth < 1024) {\n                    onClose();\n                  }\n                },\n                children: subItem.title\n              }, subItem.path, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 19\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "LayoutDashboard", "Package", "ShoppingCart", "Users", "FileText", "Settings", "BarChart3", "Truck", "CreditCard", "Bell", "Shield", "Tag", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "isOpen", "onClose", "_s", "location", "menuItems", "title", "icon", "path", "submenu", "isActiveLink", "pathname", "startsWith", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "isActive", "window", "innerWidth", "subItem", "_c", "$RefreshReg$"], "sources": ["E:/Developer/Pos_system/pos_web/src/components/layout/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport {\n  LayoutDashboard,\n  Package,\n  ShoppingCart,\n  Users,\n  FileText,\n  Settings,\n  BarChart3,\n  Truck,\n  CreditCard,\n  Bell,\n  Shield,\n  Tag,\n} from 'lucide-react';\n\nconst Sidebar = ({ isOpen, onClose }) => {\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      title: 'Dashboard',\n      icon: LayoutDashboard,\n      path: '/admin/dashboard',\n    },\n    {\n      title: 'Products',\n      icon: Package,\n      path: '/admin/pducts',\n      submenu: [\n        { title: 'All Products', path: '/admin/products' },\n        { title: 'Categories', path: '/admin/categories' },\n        { title: 'Low Stock', path: '/admin/products/low-stock' },\n      ],\n    },\n    {\n      title: 'Orders',\n      icon: ShoppingCart,\n      path: '/admin/orders',\n      submenu: [\n        { title: 'All Orders', path: '/admin/orders' },\n        { title: 'Customer Orders', path: '/admin/customer-orders' },\n        { title: 'Order Statistics', path: '/admin/orders/statistics' },\n      ],\n    },\n    {\n      title: 'Customers',\n      icon: Users,\n      path: '/admin/customers',\n    },\n    {\n      title: 'Payments',\n      icon: CreditCard,\n      path: '/admin/payments',\n    },\n    {\n      title: 'Deliveries',\n      icon: Truck,\n      path: '/admin/deliveries',\n    },\n    {\n      title: 'Reports',\n      icon: BarChart3,\n      path: '/admin/reports',\n      submenu: [\n        { title: 'Sales Report', path: '/admin/reports/sales' },\n        { title: 'Inventory Report', path: '/admin/reports/inventory' },\n        { title: 'Buy-In Sell-Out', path: '/admin/reports/buy-sell' },\n        { title: 'Customer Feedback', path: '/admin/reports/feedback' },\n        { title: 'Delivery Performance', path: '/admin/reports/delivery' },\n      ],\n    },\n    {\n      title: 'Notifications',\n      icon: Bell,\n      path: '/admin/notifications',\n    },\n    {\n      title: 'User Management',\n      icon: Shield,\n      path: '/admin/users',\n      submenu: [\n        { title: 'Users', path: '/admin/users' },\n        { title: 'Roles & Permissions', path: '/admin/permissions' },\n      ],\n    },\n    {\n      title: 'Settings',\n      icon: Settings,\n      path: '/admin/settings',\n    },\n  ];\n\n  const isActiveLink = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path + '/');\n  };\n\n  return (\n    <>\n      {/* Mobile overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex items-center justify-center h-16 px-4 bg-blue-600\">\n          <h1 className=\"text-xl font-bold text-white\">POS Admin</h1>\n        </div>\n\n        <nav className=\"mt-8 px-4\">\n          <div className=\"space-y-2\">\n            {menuItems.map((item) => (\n              <div key={item.path}>\n                <NavLink\n                  to={item.path}\n                  className={({ isActive }) =>\n                    `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${\n                      isActive || isActiveLink(item.path)\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`\n                  }\n                  onClick={() => {\n                    if (window.innerWidth < 1024) {\n                      onClose();\n                    }\n                  }}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.title}\n                </NavLink>\n\n                {/* Submenu */}\n                {item.submenu && isActiveLink(item.path) && (\n                  <div className=\"ml-8 mt-2 space-y-1\">\n                    {item.submenu.map((subItem) => (\n                      <NavLink\n                        key={subItem.path}\n                        to={subItem.path}\n                        className={({ isActive }) =>\n                          `block px-4 py-2 text-sm rounded-lg transition-colors duration-200 ${\n                            isActive\n                              ? 'bg-blue-50 text-blue-600'\n                              : 'text-gray-600 hover:bg-gray-50'\n                          }`\n                        }\n                        onClick={() => {\n                          if (window.innerWidth < 1024) {\n                            onClose();\n                          }\n                        }}\n                      >\n                        {subItem.title}\n                      </NavLink>\n                    ))}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </nav>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SACEC,eAAe,EACfC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,MAAMsB,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAEvB,eAAe;IACrBwB,IAAI,EAAE;EACR,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAEtB,OAAO;IACbuB,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,CACP;MAAEH,KAAK,EAAE,cAAc;MAAEE,IAAI,EAAE;IAAkB,CAAC,EAClD;MAAEF,KAAK,EAAE,YAAY;MAAEE,IAAI,EAAE;IAAoB,CAAC,EAClD;MAAEF,KAAK,EAAE,WAAW;MAAEE,IAAI,EAAE;IAA4B,CAAC;EAE7D,CAAC,EACD;IACEF,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAErB,YAAY;IAClBsB,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,CACP;MAAEH,KAAK,EAAE,YAAY;MAAEE,IAAI,EAAE;IAAgB,CAAC,EAC9C;MAAEF,KAAK,EAAE,iBAAiB;MAAEE,IAAI,EAAE;IAAyB,CAAC,EAC5D;MAAEF,KAAK,EAAE,kBAAkB;MAAEE,IAAI,EAAE;IAA2B,CAAC;EAEnE,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAEpB,KAAK;IACXqB,IAAI,EAAE;EACR,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAEf,UAAU;IAChBgB,IAAI,EAAE;EACR,CAAC,EACD;IACEF,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAEhB,KAAK;IACXiB,IAAI,EAAE;EACR,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAEjB,SAAS;IACfkB,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,CACP;MAAEH,KAAK,EAAE,cAAc;MAAEE,IAAI,EAAE;IAAuB,CAAC,EACvD;MAAEF,KAAK,EAAE,kBAAkB;MAAEE,IAAI,EAAE;IAA2B,CAAC,EAC/D;MAAEF,KAAK,EAAE,iBAAiB;MAAEE,IAAI,EAAE;IAA0B,CAAC,EAC7D;MAAEF,KAAK,EAAE,mBAAmB;MAAEE,IAAI,EAAE;IAA0B,CAAC,EAC/D;MAAEF,KAAK,EAAE,sBAAsB;MAAEE,IAAI,EAAE;IAA0B,CAAC;EAEtE,CAAC,EACD;IACEF,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAEd,IAAI;IACVe,IAAI,EAAE;EACR,CAAC,EACD;IACEF,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAEb,MAAM;IACZc,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,CACP;MAAEH,KAAK,EAAE,OAAO;MAAEE,IAAI,EAAE;IAAe,CAAC,EACxC;MAAEF,KAAK,EAAE,qBAAqB;MAAEE,IAAI,EAAE;IAAqB,CAAC;EAEhE,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAElB,QAAQ;IACdmB,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAME,YAAY,GAAIF,IAAI,IAAK;IAC7B,OAAOJ,QAAQ,CAACO,QAAQ,KAAKH,IAAI,IAAIJ,QAAQ,CAACO,QAAQ,CAACC,UAAU,CAACJ,IAAI,GAAG,GAAG,CAAC;EAC/E,CAAC;EAED,oBACEX,OAAA,CAAAE,SAAA;IAAAc,QAAA,GAEGZ,MAAM,iBACLJ,OAAA;MACEiB,SAAS,EAAC,qDAAqD;MAC/DC,OAAO,EAAEb;IAAQ;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACF,eAGDtB,OAAA;MACEiB,SAAS,EAAE,qJACTb,MAAM,GAAG,eAAe,GAAG,mBAAmB,EAC7C;MAAAY,QAAA,gBAEHhB,OAAA;QAAKiB,SAAS,EAAC,wDAAwD;QAAAD,QAAA,eACrEhB,OAAA;UAAIiB,SAAS,EAAC,8BAA8B;UAAAD,QAAA,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAENtB,OAAA;QAAKiB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBhB,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAD,QAAA,EACvBR,SAAS,CAACe,GAAG,CAAEC,IAAI,iBAClBxB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA,CAACf,OAAO;cACNwC,EAAE,EAAED,IAAI,CAACb,IAAK;cACdM,SAAS,EAAEA,CAAC;gBAAES;cAAS,CAAC,KACtB,6FACEA,QAAQ,IAAIb,YAAY,CAACW,IAAI,CAACb,IAAI,CAAC,GAC/B,2BAA2B,GAC3B,iCAAiC,EAExC;cACDO,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIS,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;kBAC5BvB,OAAO,CAAC,CAAC;gBACX;cACF,CAAE;cAAAW,QAAA,gBAEFhB,OAAA,CAACwB,IAAI,CAACd,IAAI;gBAACO,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACrCE,IAAI,CAACf,KAAK;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGTE,IAAI,CAACZ,OAAO,IAAIC,YAAY,CAACW,IAAI,CAACb,IAAI,CAAC,iBACtCX,OAAA;cAAKiB,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EACjCQ,IAAI,CAACZ,OAAO,CAACW,GAAG,CAAEM,OAAO,iBACxB7B,OAAA,CAACf,OAAO;gBAENwC,EAAE,EAAEI,OAAO,CAAClB,IAAK;gBACjBM,SAAS,EAAEA,CAAC;kBAAES;gBAAS,CAAC,KACtB,qEACEA,QAAQ,GACJ,0BAA0B,GAC1B,gCAAgC,EAEvC;gBACDR,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAIS,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;oBAC5BvB,OAAO,CAAC,CAAC;kBACX;gBACF,CAAE;gBAAAW,QAAA,EAEDa,OAAO,CAACpB;cAAK,GAfToB,OAAO,CAAClB,IAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBV,CACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,GA5COE,IAAI,CAACb,IAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Cd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAChB,EAAA,CA5JIH,OAAO;EAAA,QACMjB,WAAW;AAAA;AAAA4C,EAAA,GADxB3B,OAAO;AA8Jb,eAAeA,OAAO;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}