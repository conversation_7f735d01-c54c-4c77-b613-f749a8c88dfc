<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('info_users', function (Blueprint $table) {
            $table->id('info_id');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('first_name_kh')->nullable();
            $table->string('last_name_kh')->nullable();
            $table->string('fullname_en')->nullable();
            $table->string('gender', 20)->nullable();
            $table->string('date_of_birth', 10)->nullable();
            $table->string('phone', 20)->nullable();
            $table->text('current_address')->nullable();
            $table->string('photo')->nullable();
            $table->timestamps();

            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('info_users');
    }
};
