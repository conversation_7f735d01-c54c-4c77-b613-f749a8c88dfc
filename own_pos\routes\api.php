<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ProductSizeController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\CustomerOrderController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\DeliveryController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\UserController;

// Public authentication routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('logincustomer', [CustomerController::class,'login_customer']);
Route::post('/register', [AuthController::class, 'register']);

// Public customer order routes (for guest orders)
Route::prefix('public/customer-orders')->group(function () {
    Route::post('/', [CustomerOrderController::class, 'store']); // Guest order creation
    Route::get('/{orderId}', [CustomerOrderController::class, 'show']); // Order tracking by order ID
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Authentication routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh-permissions', [AuthController::class, 'refreshPermissions']);

    // User management
    Route::apiResource('users', UserController::class);

    // Product management
    Route::apiResource('products', ProductController::class);
    Route::get('/products/low-stock', [ProductController::class, 'lowStock']);

    // Product sizes management
    Route::get('/products/{product}/sizes', [ProductSizeController::class, 'index']);
    Route::post('/products/{product}/sizes', [ProductSizeController::class, 'store']);
    Route::put('/products/{product}/sizes/{size}', [ProductSizeController::class, 'update']);
    Route::delete('/products/{product}/sizes/{size}', [ProductSizeController::class, 'destroy']);
    Route::put('/products/{product}/sizes', [ProductSizeController::class, 'bulkUpdate']);
    Route::get('/products/{product}/with-prices', [ProductSizeController::class, 'getProductWithPrices']);

    // Category management
    Route::apiResource('categories', CategoryController::class);

    // Customer management
    Route::apiResource('customers', CustomerController::class);

    // Order management (Admin/Staff)
    Route::apiResource('orders', OrderController::class);
    Route::get('/orders/statistics', [OrderController::class, 'statistics']);

    // Customer Order management (Customer-facing API)
    Route::prefix('customer-orders')->group(function () {
        Route::get('/', [CustomerOrderController::class, 'index']);
        Route::post('/', [CustomerOrderController::class, 'store']);
        Route::get('/{orderId}', [CustomerOrderController::class, 'show']);
        Route::post('/{orderId}/cancel', [CustomerOrderController::class, 'cancel']);
    });

    // Payment management
    Route::apiResource('payments', PaymentController::class);
    Route::post('/payments/{payment}/process', [PaymentController::class, 'processPayment']);

    // Delivery management
    Route::apiResource('deliveries', DeliveryController::class);
    Route::post('/deliveries/{delivery}/assign-driver', [DeliveryController::class, 'assignDriver']);
    Route::post('/deliveries/{delivery}/update-status', [DeliveryController::class, 'updateStatus']);
    Route::post('/deliveries/{delivery}/update-location', [DeliveryController::class, 'updateLocation']);

    // Reporting
    Route::prefix('reports')->group(function () {
        Route::get('/sales', [ReportController::class, 'salesReport']);
        Route::get('/inventory', [ReportController::class, 'inventoryReport']);
        Route::get('/buy-in-sell-out', [ReportController::class, 'buyInSellOutReport']);
        Route::get('/customer-feedback', [ReportController::class, 'customerFeedbackReport']);
        Route::get('/delivery-performance', [ReportController::class, 'deliveryPerformanceReport']);
    });

    // Notifications
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/stock-alert', [NotificationController::class, 'stockAlert']);
        Route::post('/order-status', [NotificationController::class, 'orderStatusUpdate']);
        Route::post('/customer-feedback', [NotificationController::class, 'customerFeedback']);
        Route::post('/delivery-update', [NotificationController::class, 'deliveryUpdate']);
    });

    // Permission and Role Management
    Route::prefix('permissions')->group(function () {
        // Permission CRUD
        Route::get('/', [PermissionController::class, 'index']);
        Route::post('/', [PermissionController::class, 'store']);
        Route::get('/{permission}', [PermissionController::class, 'show']);
        Route::put('/{permission}', [PermissionController::class, 'update']);
        Route::delete('/{permission}', [PermissionController::class, 'destroy']);
        Route::post('/bulk-create', [PermissionController::class, 'bulkCreatePermissions']);

        // Role CRUD
        Route::get('/roles/list', [PermissionController::class, 'roles']);
        Route::post('/roles', [PermissionController::class, 'storeRole']);
        Route::get('/roles/{role}', [PermissionController::class, 'showRole']);
        Route::put('/roles/{role}', [PermissionController::class, 'updateRole']);
        Route::delete('/roles/{role}', [PermissionController::class, 'destroyRole']);

        // Role-Permission Management
        Route::post('/roles/{role}/assign-permissions', [PermissionController::class, 'assignPermissionsToRole']);
        Route::post('/roles/{role}/remove-permissions', [PermissionController::class, 'removePermissionsFromRole']);

        // User-Role Management
        Route::post('/users/{user}/assign-roles', [PermissionController::class, 'assignRolesToUser']);
        Route::post('/users/{user}/remove-roles', [PermissionController::class, 'removeRolesFromUser']);

        // User-Permission Management
        Route::post('/users/{user}/assign-permissions', [PermissionController::class, 'assignPermissionsToUser']);
        Route::post('/users/{user}/remove-permissions', [PermissionController::class, 'removePermissionsFromUser']);

        // User Permission Queries
        Route::get('/users/{user}/permissions', [PermissionController::class, 'getUserPermissions']);
        Route::post('/users/{user}/check-permission', [PermissionController::class, 'checkUserPermission']);
        Route::post('/users/{user}/check-role', [PermissionController::class, 'checkUserRole']);
        Route::get('/users/with-permissions', [PermissionController::class, 'getUsersWithPermissions']);
    });

    // Legacy route for compatibility
    Route::get('/user-legacy', function (Request $request) {
        return $request->user();
    });
});
