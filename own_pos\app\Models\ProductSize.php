<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductSize extends Model
{
    protected $table = 'product_sizes';
    protected $primaryKey = 'size_id';

    protected $fillable = [
        'product_id',
        'size_name_kh',
        'size_name_en',
        'price_adjustment',
        'is_available',
        'sort_order'
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the product that owns the size.
     */
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'product_id');
    }

    /**
     * Get the final price (base price + adjustment)
     */
    public function getFinalPriceAttribute()
    {
        return $this->product->price + $this->price_adjustment;
    }
}
