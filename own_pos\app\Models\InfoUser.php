<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InfoUser extends Model
{
    protected $table = 'info_users';
    protected $primaryKey = 'info_id';

    protected $fillable = [
        'user_id',
        'first_name_kh',
        'last_name_kh',
        'fullname_en',
        'gender',
        'date_of_birth',
        'phone',
        'current_address',
        'photo'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the info.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
