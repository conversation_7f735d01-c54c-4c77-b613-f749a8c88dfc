<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-800">{{ config('app.name') }}</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, {{ Auth::user()->name }}!</span>
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200">
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Success Messages -->
        @if (session('success'))
            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                <p class="text-sm text-green-600">{{ session('success') }}</p>
            </div>
        @endif

        <!-- Dashboard Content -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard</h2>
                <p class="text-gray-600 mb-6">Welcome to your dashboard! You are successfully logged in.</p>
                
                <!-- User Info Card -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 mb-3">Your Account Information</h3>
                    <div class="space-y-2" id="userInfo">
                        <p class="text-blue-800"><strong>Name:</strong> <span id="userName">{{ Auth::user()->name }}</span></p>
                        <p class="text-blue-800"><strong>Email:</strong> <span id="userEmail">{{ Auth::user()->email }}</span></p>
                        <p class="text-blue-800"><strong>Member since:</strong> <span id="memberSince">{{ Auth::user()->created_at->format('F j, Y') }}</span></p>
                        <p class="text-blue-800"><strong>Roles:</strong> <span id="userRoles" class="font-mono text-sm bg-blue-100 px-2 py-1 rounded">Loading...</span></p>
                        <p class="text-blue-800"><strong>Permissions:</strong> <span id="userPermissions" class="font-mono text-sm bg-blue-100 px-2 py-1 rounded">Loading...</span></p>
                    </div>
                </div>

                <!-- Token Info Card -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mt-6">
                    <h3 class="text-lg font-semibold text-green-900 mb-3">Authentication Token</h3>
                    <div class="space-y-2">
                        <p class="text-green-800"><strong>Token Status:</strong> <span id="tokenStatus" class="font-mono text-sm">Checking...</span></p>
                        <p class="text-green-800"><strong>Token (first 20 chars):</strong> <span id="tokenPreview" class="font-mono text-sm bg-green-100 px-2 py-1 rounded">Loading...</span></p>
                        <p class="text-green-800"><strong>Stored in:</strong> <span class="font-mono text-sm">sessionStorage</span></p>
                        <button id="refreshPermissions" class="mt-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200">
                            Refresh Permissions
                        </button>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
                            <h4 class="text-lg font-semibold mb-2">Profile Settings</h4>
                            <p class="text-sm opacity-90 mb-4">Update your profile information</p>
                            <button class="bg-white text-purple-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-100 transition duration-200">
                                Edit Profile
                            </button>
                        </div>
                        
                        <div class="bg-gradient-to-r from-green-500 to-blue-500 rounded-lg p-6 text-white">
                            <h4 class="text-lg font-semibold mb-2">Security</h4>
                            <p class="text-sm opacity-90 mb-4">Manage your account security</p>
                            <button class="bg-white text-green-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-100 transition duration-200">
                                Security Settings
                            </button>
                        </div>
                        
                        <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-6 text-white">
                            <h4 class="text-lg font-semibold mb-2">Support</h4>
                            <p class="text-sm opacity-90 mb-4">Get help and support</p>
                            <button class="bg-white text-orange-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-100 transition duration-200">
                                Contact Support
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Authentication utility functions (same as login page)
        const Auth = {
            getToken() {
                return sessionStorage.getItem('auth_token');
            },
            getUser() {
                const userData = sessionStorage.getItem('user_data');
                return userData ? JSON.parse(userData) : null;
            },
            getRoles() {
                const roles = sessionStorage.getItem('user_roles');
                return roles ? JSON.parse(roles) : [];
            },
            getPermissions() {
                const permissions = sessionStorage.getItem('user_permissions');
                return permissions ? JSON.parse(permissions) : [];
            },
            hasRole(role) {
                return this.getRoles().includes(role);
            },
            hasPermission(permission) {
                return this.getPermissions().includes(permission);
            },
            isAuthenticated() {
                return !!this.getToken();
            },
            async apiRequest(url, options = {}) {
                const token = this.getToken();
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        ...(token && { 'Authorization': `Bearer ${token}` })
                    }
                };

                const mergedOptions = {
                    ...defaultOptions,
                    ...options,
                    headers: {
                        ...defaultOptions.headers,
                        ...options.headers
                    }
                };

                return fetch(url, mergedOptions);
            }
        };

        // Update dashboard with token information
        function updateDashboard() {
            const token = Auth.getToken();
            const user = Auth.getUser();
            const roles = Auth.getRoles();
            const permissions = Auth.getPermissions();

            // Update token status
            const tokenStatus = document.getElementById('tokenStatus');
            const tokenPreview = document.getElementById('tokenPreview');

            if (token) {
                tokenStatus.textContent = 'Active';
                tokenStatus.className = 'font-mono text-sm bg-green-200 text-green-800 px-2 py-1 rounded';
                tokenPreview.textContent = token.substring(0, 20) + '...';
            } else {
                tokenStatus.textContent = 'Not Found';
                tokenStatus.className = 'font-mono text-sm bg-red-200 text-red-800 px-2 py-1 rounded';
                tokenPreview.textContent = 'No token available';
            }

            // Update user info if available from sessionStorage
            if (user) {
                document.getElementById('userName').textContent = user.name;
                document.getElementById('userEmail').textContent = user.email;
                if (user.created_at) {
                    const date = new Date(user.created_at);
                    document.getElementById('memberSince').textContent = date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                }
            }

            // Update roles
            const userRolesElement = document.getElementById('userRoles');
            if (roles.length > 0) {
                userRolesElement.textContent = roles.join(', ');
                userRolesElement.className = 'font-mono text-sm bg-blue-100 px-2 py-1 rounded';
            } else {
                userRolesElement.textContent = 'No roles assigned';
                userRolesElement.className = 'font-mono text-sm bg-gray-100 px-2 py-1 rounded';
            }

            // Update permissions
            const userPermissionsElement = document.getElementById('userPermissions');
            if (permissions.length > 0) {
                userPermissionsElement.textContent = permissions.length + ' permissions';
                userPermissionsElement.title = permissions.join(', ');
                userPermissionsElement.className = 'font-mono text-sm bg-blue-100 px-2 py-1 rounded cursor-help';
            } else {
                userPermissionsElement.textContent = 'No permissions assigned';
                userPermissionsElement.className = 'font-mono text-sm bg-gray-100 px-2 py-1 rounded';
            }

            // Log to console for debugging
            console.log('Dashboard Auth Info:', {
                token: token ? token.substring(0, 20) + '...' : 'None',
                user: user,
                roles: roles,
                permissions: permissions
            });
        }

        // Refresh permissions from API
        async function refreshPermissions() {
            const button = document.getElementById('refreshPermissions');
            const originalText = button.textContent;

            button.textContent = 'Refreshing...';
            button.disabled = true;

            try {
                const response = await Auth.apiRequest('/api/refresh-permissions', {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.success) {
                        // Update sessionStorage with new permissions
                        sessionStorage.setItem('user_roles', JSON.stringify(data.data.roles));
                        sessionStorage.setItem('user_permissions', JSON.stringify(data.data.permissions));

                        // Update dashboard display
                        updateDashboard();

                        alert('Permissions refreshed successfully!');
                    } else {
                        alert('Failed to refresh permissions: ' + data.message);
                    }
                } else {
                    alert('Failed to refresh permissions');
                }
            } catch (error) {
                console.error('Error refreshing permissions:', error);
                alert('Error refreshing permissions');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();

            // Add event listener for refresh button
            document.getElementById('refreshPermissions').addEventListener('click', refreshPermissions);

            // Check authentication status
            if (!Auth.isAuthenticated()) {
                console.warn('No authentication token found in sessionStorage');
            }
        });
    </script>
</body>
</html>
