<?php

namespace App\Http\Controllers\Api;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class RolePemissionControllers extends Controller
{
    public function index()
    {
        try{
            $roles = Role::with('permissions')->get();
            $fotmatRoles = $roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'permissions' => $role->permissions->map(function ($permission) {
                        return [
                            'id' => $permission->id,
                            'name' => $permission->name
                        ];
                    })
                ];
            });
            return response()->json([
                'success' => true,
                'data' => $fotmatRoles
            ]);
        }catch(\Exception $e){
            Log::error('Role error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve roles',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function store(Request $request){
        try {
             $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:roles,name',
                'permission_ids' => 'array',
                'permission_ids.*' => 'exists:permissions,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $role = Role::create([
                'name' => strtolower($request->name),
                'guard_name' => 'api' // Change to match your existing roles
            ]);

            if ($request->has('permission_ids')) {
                $permissions = Permission::whereIn('id', $request->permission_ids)->get();
                $role->syncPermissions($permissions);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Role created successfully',
                'data' => $role->load(['permissions'])
            ], 201);
        } catch (\Exception $e) {
            Log::error('Role error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create role',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:roles,name,' . $id,
                'permission_ids' => 'array',
                'permission_ids.*' => 'exists:permissions,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $role = Role::findOrFail($id);

            if (in_array($role->name, ['superadmin', 'admin'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot modify system roles'
                ], 403);
            }

            $role->update(['name' => strtolower($request->name)]);

            if ($request->has('permission_ids')) {
                $permissions = Permission::whereIn('id', $request->permission_ids)->get();
                $role->syncPermissions($permissions);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Role updated successfully',
                'data' => $role->fresh()->load(['permissions'])
            ]);
        } catch (\Exception $e) {
            Log::error('Role update error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update role',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function show($id)
    {
        try {
            $role = Role::where('guard_name', 'api')
                ->findOrFail($id);

            return response()->json([
                'status' => 'success',
                'message' => 'Role retrieved successfully',
                'data' => $role->load(['permissions'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Role not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }
    public function destroy($id)
    {
      
        $role = Role::findOrFail($id);

        if (in_array($role->name, ['superadmin', 'admin'])) {
            return response()->json(['status' => 'error', 'message' => 'Cannot delete system role'], 403);
        }

        if ($role->users()->exists()) {
            return response()->json(['status' => 'error', 'message' => 'Cannot delete role with assigned users'], 400);
        }

        $role->syncPermissions([]);
        $role->delete();

        return response()->json(['status' => 'success', 'message' => 'Role deleted successfully'], 200);
    }
}
