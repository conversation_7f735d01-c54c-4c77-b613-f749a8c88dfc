{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 11a9 9 0 0 1 9 9\",\n  key: \"pv89mb\"\n}], [\"path\", {\n  d: \"M4 4a16 16 0 0 1 16 16\",\n  key: \"k0647b\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"bfqh0e\"\n}]];\nconst Rss = createLucideIcon(\"rss\", __iconNode);\nexport { __iconNode, Rss as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Rss", "createLucideIcon"], "sources": ["E:\\Developer\\Pos_system\\pos_web\\node_modules\\lucide-react\\src\\icons\\rss.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 11a9 9 0 0 1 9 9', key: 'pv89mb' }],\n  ['path', { d: 'M4 4a16 16 0 0 1 16 16', key: 'k0647b' }],\n  ['circle', { cx: '5', cy: '19', r: '1', key: 'bfqh0e' }],\n];\n\n/**\n * @component @name Rss\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMWE5IDkgMCAwIDEgOSA5IiAvPgogIDxwYXRoIGQ9Ik00IDRhMTYgMTYgMCAwIDEgMTYgMTYiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjE5IiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rss\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rss = createLucideIcon('rss', __iconNode);\n\nexport default Rss;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,GAAA,GAAMC,gBAAiB,QAAOP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}