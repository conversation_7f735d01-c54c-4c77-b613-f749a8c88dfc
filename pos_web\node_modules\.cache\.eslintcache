[{"E:\\Developer\\Pos_system\\pos_web\\src\\index.js": "1", "E:\\Developer\\Pos_system\\pos_web\\src\\reportWebVitals.js": "2", "E:\\Developer\\Pos_system\\pos_web\\src\\App.js": "3", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Products.js": "4", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Dashboard.js": "5", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\ProtectedRoute.js": "6", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\AdminLayout.js": "7", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\ErrorBoundary.js": "8", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\Login.js": "9", "E:\\Developer\\Pos_system\\pos_web\\src\\contexts\\AuthContext.js": "10", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\profile\\index.js": "11", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Sidebar.js": "12", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Header.js": "13", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\SalesChart.js": "14", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\StatCard.js": "15", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\TrafficSources.js": "16", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\RevenueChart.js": "17", "E:\\Developer\\Pos_system\\pos_web\\src\\services\\api.js": "18", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\form\\Alert.js": "19", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\LowStockProducts.js": "20"}, {"size": 535, "mtime": 1751711868149, "results": "21", "hashOfConfig": "22"}, {"size": 362, "mtime": 1751711869287, "results": "23", "hashOfConfig": "22"}, {"size": 6307, "mtime": 1751881276246, "results": "24", "hashOfConfig": "22"}, {"size": 122, "mtime": 1751877470604, "results": "25", "hashOfConfig": "22"}, {"size": 7017, "mtime": 1751712546726, "results": "26", "hashOfConfig": "22"}, {"size": 1422, "mtime": 1751737891009, "results": "27", "hashOfConfig": "22"}, {"size": 962, "mtime": 1751712456137, "results": "28", "hashOfConfig": "22"}, {"size": 1962, "mtime": 1751736720343, "results": "29", "hashOfConfig": "22"}, {"size": 7726, "mtime": 1751782608742, "results": "30", "hashOfConfig": "22"}, {"size": 711, "mtime": 1751736815120, "results": "31", "hashOfConfig": "22"}, {"size": 31001, "mtime": 1751876281954, "results": "32", "hashOfConfig": "22"}, {"size": 5063, "mtime": 1751881252066, "results": "33", "hashOfConfig": "22"}, {"size": 8058, "mtime": 1751787842502, "results": "34", "hashOfConfig": "22"}, {"size": 3556, "mtime": 1751712492167, "results": "35", "hashOfConfig": "22"}, {"size": 1595, "mtime": 1751712473664, "results": "36", "hashOfConfig": "22"}, {"size": 1581, "mtime": 1751712518045, "results": "37", "hashOfConfig": "22"}, {"size": 2819, "mtime": 1751712507381, "results": "38", "hashOfConfig": "22"}, {"size": 7842, "mtime": 1751794277171, "results": "39", "hashOfConfig": "22"}, {"size": 1512, "mtime": 1751782426903, "results": "40", "hashOfConfig": "22"}, {"size": 7801, "mtime": 1751877263465, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sznllr", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Developer\\Pos_system\\pos_web\\src\\index.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\reportWebVitals.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\App.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Products.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Dashboard.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\ProtectedRoute.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\AdminLayout.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\ErrorBoundary.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\Login.js", ["102"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\contexts\\AuthContext.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\profile\\index.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Sidebar.js", ["103", "104"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Header.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\SalesChart.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\StatCard.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\TrafficSources.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\RevenueChart.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\services\\api.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\form\\Alert.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\LowStockProducts.js", [], [], {"ruleId": "105", "severity": 1, "message": "106", "line": 3, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 3, "endColumn": 21}, {"ruleId": "105", "severity": 1, "message": "109", "line": 8, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 8, "endColumn": 11}, {"ruleId": "105", "severity": 1, "message": "110", "line": 15, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 15, "endColumn": 6}, "no-unused-vars", "'useNavigate' is defined but never used.", "Identifier", "unusedVar", "'FileText' is defined but never used.", "'Tag' is defined but never used."]