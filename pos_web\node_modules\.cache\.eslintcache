[{"E:\\Developer\\Pos_system\\pos_web\\src\\index.js": "1", "E:\\Developer\\Pos_system\\pos_web\\src\\reportWebVitals.js": "2", "E:\\Developer\\Pos_system\\pos_web\\src\\App.js": "3", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Products.js": "4", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Dashboard.js": "5", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\ProtectedRoute.js": "6", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\AdminLayout.js": "7", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\ErrorBoundary.js": "8", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\Login.js": "9", "E:\\Developer\\Pos_system\\pos_web\\src\\contexts\\AuthContext.js": "10", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\profile\\index.js": "11", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Sidebar.js": "12", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Header.js": "13", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\SalesChart.js": "14", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\StatCard.js": "15", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\TrafficSources.js": "16", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\RevenueChart.js": "17", "E:\\Developer\\Pos_system\\pos_web\\src\\services\\api.js": "18", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\form\\Alert.js": "19"}, {"size": 535, "mtime": 1751711868149, "results": "20", "hashOfConfig": "21"}, {"size": 362, "mtime": 1751711869287, "results": "22", "hashOfConfig": "21"}, {"size": 6294, "mtime": 1751787115621, "results": "23", "hashOfConfig": "21"}, {"size": 8010, "mtime": 1751877107716, "results": "24", "hashOfConfig": "21"}, {"size": 7017, "mtime": 1751712546726, "results": "25", "hashOfConfig": "21"}, {"size": 1422, "mtime": 1751737891009, "results": "26", "hashOfConfig": "21"}, {"size": 962, "mtime": 1751712456137, "results": "27", "hashOfConfig": "21"}, {"size": 1962, "mtime": 1751736720343, "results": "28", "hashOfConfig": "21"}, {"size": 7726, "mtime": 1751782608742, "results": "29", "hashOfConfig": "21"}, {"size": 711, "mtime": 1751736815120, "results": "30", "hashOfConfig": "21"}, {"size": 31001, "mtime": 1751876281954, "results": "31", "hashOfConfig": "21"}, {"size": 5075, "mtime": 1751712424028, "results": "32", "hashOfConfig": "21"}, {"size": 8058, "mtime": 1751787842502, "results": "33", "hashOfConfig": "21"}, {"size": 3556, "mtime": 1751712492167, "results": "34", "hashOfConfig": "21"}, {"size": 1595, "mtime": 1751712473664, "results": "35", "hashOfConfig": "21"}, {"size": 1581, "mtime": 1751712518045, "results": "36", "hashOfConfig": "21"}, {"size": 2819, "mtime": 1751712507381, "results": "37", "hashOfConfig": "21"}, {"size": 7842, "mtime": 1751794277171, "results": "38", "hashOfConfig": "21"}, {"size": 1512, "mtime": 1751782426903, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sznllr", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Developer\\Pos_system\\pos_web\\src\\index.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\reportWebVitals.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\App.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Products.js", ["97"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Dashboard.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\ProtectedRoute.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\AdminLayout.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\ErrorBoundary.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\Login.js", ["98"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\contexts\\AuthContext.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\profile\\index.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Sidebar.js", ["99", "100"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Header.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\SalesChart.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\StatCard.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\TrafficSources.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\RevenueChart.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\services\\api.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\form\\Alert.js", [], [], {"ruleId": "101", "severity": 1, "message": "102", "line": 10, "column": 10, "nodeType": "103", "messageId": "104", "endLine": 10, "endColumn": 24}, {"ruleId": "101", "severity": 1, "message": "105", "line": 3, "column": 10, "nodeType": "103", "messageId": "104", "endLine": 3, "endColumn": 21}, {"ruleId": "101", "severity": 1, "message": "106", "line": 8, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 8, "endColumn": 11}, {"ruleId": "101", "severity": 1, "message": "107", "line": 15, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 15, "endColumn": 6}, "no-unused-vars", "'editingProduct' is assigned a value but never used.", "Identifier", "unusedVar", "'useNavigate' is defined but never used.", "'FileText' is defined but never used.", "'Tag' is defined but never used."]