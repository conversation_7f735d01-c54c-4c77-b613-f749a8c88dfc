[{"E:\\Developer\\Pos_system\\pos_web\\src\\index.js": "1", "E:\\Developer\\Pos_system\\pos_web\\src\\App.js": "2", "E:\\Developer\\Pos_system\\pos_web\\src\\reportWebVitals.js": "3", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Products.js": "4", "E:\\Developer\\Pos_system\\pos_web\\src\\contexts\\AuthContext.js": "5", "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Dashboard.js": "6", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\ProtectedRoute.js": "7", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\Login.js": "8", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\AdminLayout.js": "9", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Sidebar.js": "10", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Header.js": "11", "E:\\Developer\\Pos_system\\pos_web\\src\\services\\api.js": "12", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\SalesChart.js": "13", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\StatCard.js": "14", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\TrafficSources.js": "15", "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\RevenueChart.js": "16"}, {"size": 535, "mtime": 1751711868149, "results": "17", "hashOfConfig": "18"}, {"size": 3515, "mtime": 1751712636572, "results": "19", "hashOfConfig": "18"}, {"size": 362, "mtime": 1751711869287, "results": "20", "hashOfConfig": "18"}, {"size": 7872, "mtime": 1751712620702, "results": "21", "hashOfConfig": "18"}, {"size": 2958, "mtime": 1751712366599, "results": "22", "hashOfConfig": "18"}, {"size": 7017, "mtime": 1751712546726, "results": "23", "hashOfConfig": "18"}, {"size": 1802, "mtime": 1751712395499, "results": "24", "hashOfConfig": "18"}, {"size": 4847, "mtime": 1751712384294, "results": "25", "hashOfConfig": "18"}, {"size": 962, "mtime": 1751712456137, "results": "26", "hashOfConfig": "18"}, {"size": 5075, "mtime": 1751712424028, "results": "27", "hashOfConfig": "18"}, {"size": 5793, "mtime": 1751712446758, "results": "28", "hashOfConfig": "18"}, {"size": 7062, "mtime": 1751712953378, "results": "29", "hashOfConfig": "18"}, {"size": 3556, "mtime": 1751712492167, "results": "30", "hashOfConfig": "18"}, {"size": 1595, "mtime": 1751712473664, "results": "31", "hashOfConfig": "18"}, {"size": 1581, "mtime": 1751712518045, "results": "32", "hashOfConfig": "18"}, {"size": 2819, "mtime": 1751712507381, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sznllr", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Developer\\Pos_system\\pos_web\\src\\index.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\App.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\reportWebVitals.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Products.js", ["82"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\contexts\\AuthContext.js", ["83"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\pages\\Dashboard.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\ProtectedRoute.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\auth\\Login.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\AdminLayout.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Sidebar.js", ["84", "85"], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\layout\\Header.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\services\\api.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\SalesChart.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\StatCard.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\TrafficSources.js", [], [], "E:\\Developer\\Pos_system\\pos_web\\src\\components\\dashboard\\RevenueChart.js", [], [], {"ruleId": "86", "severity": 1, "message": "87", "line": 10, "column": 10, "nodeType": "88", "messageId": "89", "endLine": 10, "endColumn": 24}, {"ruleId": "90", "severity": 1, "message": "91", "line": 21, "column": 6, "nodeType": "92", "endLine": 21, "endColumn": 8, "suggestions": "93"}, {"ruleId": "86", "severity": 1, "message": "94", "line": 8, "column": 3, "nodeType": "88", "messageId": "89", "endLine": 8, "endColumn": 11}, {"ruleId": "86", "severity": 1, "message": "95", "line": 15, "column": 3, "nodeType": "88", "messageId": "89", "endLine": 15, "endColumn": 6}, "no-unused-vars", "'editingProduct' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["96"], "'FileText' is defined but never used.", "'Tag' is defined but never used.", {"desc": "97", "fix": "98"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "99", "text": "100"}, [599, 601], "[checkAuthStatus]"]