<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\InfoCustomer;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Storage;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function index(Request $request): JsonResponse
    {

        try {
            
            $customer = InfoCustomer::with('user','orders')->get();
            return response()->json([
                'success' => true,
                'message' => 'Customers retrieved successfully',
                'data' => $customer,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving customers: ' . $e->getMessage()
            ], 500);
        }
    }
    public function login_customer(Request $request)
    {
        try{
            $validator = Validator::make($request->all(),[
                'email' => 'required|email',
                'password' => 'required|string|min:6', 
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ],422);
            }
            $user = User::where('email',$request->email)->first();
            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid credentials'
                ], 401);
            }
            $user->tokens()->delete();
            // Create new token
            $token = $user->createToken('auth-token')->plainTextToken;

            // Log successful login
            Log::info('Successful login', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => $request->ip()
            ]);
            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'email_verified_at' => $user->email_verified_at,
                        'created_at' => $user->created_at,
                        'updated_at' => $user->updated_at,
                    ],
                    'customer' => InfoCustomer::where('user_id','=',$user->id)->first(),
                    'token' => $token,
                    'expires_at' => null // Sanctum tokens don't expire by default
                ]
            ], 200);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Error logging in: ' . $e->getMessage()
            ], 500);
        }
    }
    public function store(Request $request)
    {
        try{
            $validator = Validator::make($request->all(),[
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:6',
                'first_name_kh' => 'required|string|max:255',
                'last_name_kh' => 'required|string|max:255',
                'gender' => 'required|in:male,female,other',
                'date_of_birth' => 'required|date',
                'phone' => 'required|string|max:20|unique:info_customers,phone',
                'current_address' => 'required|string|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            $photoPath = null;
            if ($request->hasFile('photo')) {
                $photoPath = $request->file('photo')->store('customer_photos', 'public');
            }
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            $customer = InfoCustomer::create([
                'user_id' => $user->id,
                'first_name_kh' => $request->first_name_kh,
                'last_name_kh' => $request->last_name_kh,
                'fullname_kh' => $request->name,
                'gender' => $request->gender,
                'date_of_birth' => $request->date_of_birth,
                'phone' => $request->phone,
                'current_address' => $request->current_address,
                'photo' => $photoPath,
            ]);
             $iconCustomer = InfoCustomer::with('user')->find($customer->customer_id);
            return response()->json([
                'success' => true,
                'message' => 'Registration successful',
                'data' => $iconCustomer
            ],201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Error registering: ' . $e->getMessage()
            ], 500);
        }
    }
    /**
     * Store a newly created resource in storage.
     */
    public function show(Request $request, $id): JsonResponse
    {
        try {
            $customer = InfoCustomer::with('user' , 'orders.orderItems.product','orders.orderItems.size', 'orders.deliveryInfo.driver', 'orders.payments')->findOrFail($id);
            return response()->json([
                'status' => 'success',
                'message' => 'User retrieved successfully',
                'data' => $customer
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
{
    try {
        $customer = InfoCustomer::findOrFail($id);
        $user = User::findOrFail($customer->user_id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'sometimes|string|min:6',
            'first_name_kh' => 'sometimes|string|max:100',
            'last_name_kh' => 'sometimes|string|max:100',
            'phone' => 'sometimes|string|max:20|unique:info_customers,phone,' . $id . ',customer_id',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date',
            'current_address' => 'sometimes|string|max:255', // fixed typo
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $validator->validated();

        $photoPath = $customer->photo;
        if ($request->hasFile('photo')) {
            if ($photoPath && Storage::disk('public')->exists($photoPath)) {
                Storage::disk('public')->delete($photoPath);
            }
            $photoPath = $request->file('photo')->store('customer_photos', 'public');
        }

        $userUpdate = [];
        if (isset($validated['name'])) $userUpdate['name'] = $validated['name'];
        if (isset($validated['email'])) $userUpdate['email'] = $validated['email'];
        if (isset($validated['password'])) $userUpdate['password'] = Hash::make($validated['password']);

        $customerUpdate = [];
        if (isset($validated['first_name_kh'])) $customerUpdate['first_name_kh'] = $validated['first_name_kh'];
        if (isset($validated['last_name_kh'])) $customerUpdate['last_name_kh'] = $validated['last_name_kh'];
        if (isset($validated['name'])) $customerUpdate['fullname_kh'] = $validated['name'];
        if (isset($validated['gender'])) $customerUpdate['gender'] = $validated['gender'];
        if (isset($validated['date_of_birth'])) $customerUpdate['date_of_birth'] = $validated['date_of_birth'];
        if (isset($validated['phone'])) $customerUpdate['phone'] = $validated['phone'];
        if (isset($validated['current_address'])) $customerUpdate['current_address'] = $validated['current_address'];
        if ($request->hasFile('photo')) $customerUpdate['photo'] = $photoPath;

        if (!empty($userUpdate)) {
            $user->update($userUpdate);
        }
        if (!empty($customerUpdate)) {
            $customer->update($customerUpdate);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'customer' => $customer
            ],
            'message' => 'Customer updated successfully'
        ]);

    } catch (\Illuminate\Validation\ValidationException $e) {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $e->errors()
        ], 422);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error updating customer: ' . $e->getMessage()
        ], 500);
    }
}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $customer = InfoCustomer::findOrFail($id);
            // Check if customer has orders
            if ($customer->orders()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete customer with existing orders'
                ], 400);
            }

             // Optionally delete the user as well
            $user = $customer->user;
            $customer->delete();
            if ($user) {
                $user->delete();
            }

            return response()->json([
                'success' => true,
                'message' => 'Customer deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting customer: ' . $e->getMessage()
            ], 500);
        }
    }
}
