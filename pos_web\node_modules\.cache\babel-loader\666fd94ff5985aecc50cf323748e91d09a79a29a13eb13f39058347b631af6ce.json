{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 21V10a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v11\",\n  key: \"pb2vm6\"\n}], [\"path\", {\n  d: \"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 1.132-1.803l7.95-3.974a2 2 0 0 1 1.837 0l7.948 3.974A2 2 0 0 1 22 8z\",\n  key: \"doq5xv\"\n}], [\"path\", {\n  d: \"M6 13h12\",\n  key: \"yf64js\"\n}], [\"path\", {\n  d: \"M6 17h12\",\n  key: \"1jwigz\"\n}]];\nconst Warehouse = createLucideIcon(\"warehouse\", __iconNode);\nexport { __iconNode, Warehouse as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Warehouse", "createLucideIcon"], "sources": ["E:\\Developer\\Pos_system\\pos_web\\node_modules\\lucide-react\\src\\icons\\warehouse.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 21V10a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v11', key: 'pb2vm6' }],\n  [\n    'path',\n    {\n      d: 'M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 1.132-1.803l7.95-3.974a2 2 0 0 1 1.837 0l7.948 3.974A2 2 0 0 1 22 8z',\n      key: 'doq5xv',\n    },\n  ],\n  ['path', { d: 'M6 13h12', key: 'yf64js' }],\n  ['path', { d: 'M6 17h12', key: '1jwigz' }],\n];\n\n/**\n * @component @name Warehouse\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMjFWMTBhMSAxIDAgMCAwLTEtMUg3YTEgMSAwIDAgMC0xIDF2MTEiIC8+CiAgPHBhdGggZD0iTTIyIDE5YTIgMiAwIDAgMS0yIDJINGEyIDIgMCAwIDEtMi0yVjhhMiAyIDAgMCAxIDEuMTMyLTEuODAzbDcuOTUtMy45NzRhMiAyIDAgMCAxIDEuODM3IDBsNy45NDggMy45NzRBMiAyIDAgMCAxIDIyIDh6IiAvPgogIDxwYXRoIGQ9Ik02IDEzaDEyIiAvPgogIDxwYXRoIGQ9Ik02IDE3aDEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/warehouse\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Warehouse = createLucideIcon('warehouse', __iconNode);\n\nexport default Warehouse;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}