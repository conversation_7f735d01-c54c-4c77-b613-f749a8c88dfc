<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryNotification extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'delivery_notifications';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'notification_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'delivery_id',
        'customer_phone',
        'customer_email',
        'notification_type',
        'message',
        'status',
        'sent_at',
        'delivered_at'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime'
    ];

    /**
     * Get the delivery that owns the notification.
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(DeliveryInfo::class, 'delivery_id', 'delivery_id');
    }

    /**
     * Scope a query to only include notifications with a specific status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include notifications of a specific type.
     */
    public function scopeType($query, $type)
    {
        return $query->where('notification_type', $type);
    }
}
