import React from 'react';

const TrafficSources = ({ data = [] }) => {
  const defaultData = [
    { source: 'Direct', percentage: 60, color: 'bg-blue-500' },
    { source: 'Social', percentage: 50, color: 'bg-green-500' },
    { source: 'Referral', percentage: 30, color: 'bg-yellow-500' },
    { source: 'Bounce', percentage: 60, color: 'bg-red-500' },
    { source: 'Internet', percentage: 40, color: 'bg-purple-500' },
  ];

  const trafficData = data.length > 0 ? data : defaultData;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Traffic Sources</h3>
      
      <div className="space-y-4">
        {trafficData.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center flex-1">
              <span className="text-sm font-medium text-gray-700 w-16">
                {item.source}
              </span>
              <div className="flex-1 mx-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${item.color}`}
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
            <span className="text-sm font-medium text-gray-900 w-8 text-right">
              {item.percentage}%
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TrafficSources;
