import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import { Alert } from "../form/Alert";
import { authAPI } from "../../services/api";
const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState({});
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [alert, setAlert] = useState({
    visible: false,
    message: "",
    status: "success",
  });
  const { login } = useAuth();
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError((prev) => ({
      ...prev,
      [name]: "", // clear this field's error
    }));
  };
  const validator = () => {
    const newErrors = {};
    if (!formData.email) {
      newErrors.email = "Email is required.";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email address is invalid.";
    }
    if (!formData.password) {
      newErrors.password = "Password is required.";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters.";
    }
    return newErrors;
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    const validatorError = validator();

    if (Object.keys(validatorError).length > 0) {
      setError(validatorError);
      setLoading(false);
      return;
    }
    setError({});
    try {
      const response = await authAPI.login(formData, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const userData = {
        user: response.data.user,
        token: response.data.token,
        permissions: response.data.permissions,
        roles: response.data.roles,
      };
      setAlert({
        visible: true,
        message: response.message,
        status: "success",
      });
      sessionStorage.setItem("user", JSON.stringify(userData));
      setTimeout(() => {
        setAlert({
          visible: false,
          message: "",
          status: "success",
        });
        login();
      }, 1500);
    } catch (error) {
      setLoading(false);
      setAlert({
        visible: true,
        message:
          error.response?.data?.message || "Login failed. Please try again.",
        status: "error",
      });
      setError({
        email: "Login failed. Please check your email address!",
        password: "Login failed. Please check your Password!",
      });
      setTimeout(() => {
        setAlert({
          visible: false,
          message: "",
          status: "error",
        });
      }, 1500);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-gray-900">Admin Login</h2>
          <p className="mt-2 text-sm text-gray-600">
            Sign in to your admin account
          </p>
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-xs text-blue-700 font-medium">
              Demo Credentials:
            </p>
            <p className="text-xs text-blue-600">
              Email: <EMAIL>
            </p>
            <p className="text-xs text-blue-600">Password: secret</p>
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="w-[100%]">
              <label
                htmlFor="email"
                className="flex items-start p-1 text-sm font-medium text-gray-700"
              >
                Email Address
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your email"
                />
              </div>
              {error.email && (
                <div className="flex items-start mt-1 text-sm text-red-500">{error.email}</div>
              )}
            </div>

            <div className="w-[100%]">
              <div>
                <label
                  htmlFor="password"
                  className="flex items-start p-1 text-sm font-medium text-gray-700"
                >
                  Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    value={formData.password}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your password"
                  />

                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              {error.password && (
                <div className="flex items-start mt-1 text-sm text-red-500">
                  {error.password}
                </div>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing in...
                </div>
              ) : (
                "Sign in"
              )}
            </button>
          </div>
        </form>
      </div>
      <Alert
        message={alert.message}
        visible={alert.visible}
        status={alert.status}
        onClose={() => setAlert({ ...alert, visible: false })}
      />
    </div>
  );
};

export default Login;
