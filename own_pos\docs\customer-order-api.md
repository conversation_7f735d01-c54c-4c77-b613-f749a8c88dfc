# Customer Order API Documentation

This API is designed for React and Flutter frontends to handle customer orders in the POS system.

## Base URL
```
http://your-domain.com/api
```

## Authentication
- Protected routes require `Authorization: Bearer {token}` header
- Public routes don't require authentication

## Endpoints

### 1. Create Customer Order (Public/Protected)

**POST** `/public/customer-orders` (for guest orders)
**POST** `/customer-orders` (for authenticated customers)

**Request Body:**
```json
{
  "customer_id": 1,
  "order_type": "delivery",
  "notes": "Extra spicy please",
  "delivery_address": "123 Main St, City",
  "customer_phone": "+1234567890",
  "items": [
    {
      "product_id": 1,
      "size_id": 2,
      "quantity": 2,
      "notes": "No onions"
    },
    {
      "product_id": 3,
      "quantity": 1
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": 1,
    "order_number": "ORD-20250705-0001",
    "customer_id": 1,
    "order_type": "delivery",
    "status": "pending",
    "subtotal": "15.50",
    "tax_amount": "1.55",
    "delivery_fee": "2.00",
    "total_amount": "19.05",
    "order_date": "2025-07-05T10:30:00Z",
    "orderItems": [...],
    "customer": {...},
    "deliveryInfo": {...}
  },
  "message": "Customer order created successfully"
}
```

### 2. Get Customer Orders

**GET** `/customer-orders?customer_id=1`

**Query Parameters:**
- `customer_id` (required): Customer ID
- `status`: Filter by order status (pending, preparing, ready, completed, cancelled)
- `order_type`: Filter by order type (pickup, delivery, dine_in)
- `date_from`: Filter from date (YYYY-MM-DD)
- `date_to`: Filter to date (YYYY-MM-DD)
- `sort_by`: Sort field (default: order_date)
- `sort_order`: Sort direction (asc/desc, default: desc)
- `per_page`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [...orders...],
    "total": 25,
    "per_page": 10
  },
  "message": "Customer orders retrieved successfully"
}
```

### 3. Get Specific Order

**GET** `/customer-orders/{orderId}?customer_id=1`
**GET** `/public/customer-orders/{orderId}` (for order tracking)

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": 1,
    "order_number": "ORD-20250705-0001",
    "status": "preparing",
    "orderItems": [
      {
        "product": {
          "name_en": "Burger",
          "sizes": [...]
        },
        "size": {
          "size_name_en": "Large"
        },
        "quantity": 2,
        "unit_price": "7.75",
        "total_price": "15.50"
      }
    ],
    "deliveryInfo": {
      "delivery_address": "123 Main St",
      "delivery_status": "pending"
    }
  },
  "message": "Customer order retrieved successfully"
}
```

### 4. Cancel Order

**POST** `/customer-orders/{orderId}/cancel?customer_id=1`

**Response:**
```json
{
  "success": true,
  "message": "Order cancelled successfully"
}
```

## React Example

```javascript
// Create order
const createOrder = async (orderData) => {
  try {
    const response = await fetch('/api/public/customer-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer ' + token // if authenticated
      },
      body: JSON.stringify(orderData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('Order created:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

// Get customer orders
const getCustomerOrders = async (customerId, filters = {}) => {
  const params = new URLSearchParams({
    customer_id: customerId,
    ...filters
  });
  
  try {
    const response = await fetch(`/api/customer-orders?${params}`, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });
    
    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};
```

## Flutter Example

```dart
// Order service class
class OrderService {
  static const String baseUrl = 'http://your-domain.com/api';
  
  static Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/public/customer-orders'),
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer $token', // if authenticated
      },
      body: jsonEncode(orderData),
    );
    
    final result = jsonDecode(response.body);
    
    if (result['success']) {
      return result['data'];
    } else {
      throw Exception(result['message']);
    }
  }
  
  static Future<List<dynamic>> getCustomerOrders(int customerId, {Map<String, String>? filters}) async {
    final params = {'customer_id': customerId.toString()};
    if (filters != null) params.addAll(filters);
    
    final uri = Uri.parse('$baseUrl/customer-orders').replace(queryParameters: params);
    
    final response = await http.get(
      uri,
      headers: {
        'Authorization': 'Bearer $token',
      },
    );
    
    final result = jsonDecode(response.body);
    return result['data']['data'];
  }
}
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `404`: Not Found
- `422`: Validation Error
- `500`: Server Error
